# WebSocket Integration Guide

## Overview

All WebSocket APIs have been enhanced with heartbeat detection, connection state management, and coordinated reconnection capabilities. This guide shows how to integrate the updated APIs into your components.

## Updated WebSocket APIs

### Enhanced APIs
- **HealthWSApi** - Master heartbeat coordinator
- **BroadcastEventWSApi** - Event broadcasting with heartbeat
- **TagsWSApi** - Tag data with heartbeat monitoring
- **NodesWSApi** - Node data with heartbeat monitoring  
- **LogWSApi** - Log entries with heartbeat monitoring

### New Services
- **WebSocketManagerService** - Centralized connection management

## Integration Steps

### 1. Import Required Services

```typescript
import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { 
  HealthWSA<PERSON>, 
  TagsWSApi, 
  NodesWSApi, 
  LogWSApi, 
  BroadcastEventWSApi,
  WebSocketManagerService 
} from './data/wsApi/wsApi';
```

### 2. Set Up Component

```typescript
@Component({
  selector: 'app-your-component',
  templateUrl: './your-component.html'
})
export class YourComponent implements OnInit, OnDestroy {
  private subscriptions: Subscription[] = [];
  private websockets: { [key: string]: WebSocket } = {};
  
  constructor(
    private healthWSApi: HealthWSApi,
    private tagsWSApi: TagsWSApi,
    private nodesWSApi: NodesWSApi,
    private logWSApi: LogWSApi,
    private broadcastEventWSApi: BroadcastEventWSApi,
    private wsManager: WebSocketManagerService
  ) {}
}
```

### 3. Initialize WebSocket Manager

```typescript
ngOnInit(): void {
  this.setupWebSocketManager();
  this.initializeConnections();
}

private setupWebSocketManager(): void {
  // Register all WebSocket connections
  this.wsManager.registerConnection({
    name: 'health',
    reconnectFunction: () => this.healthWSApi.forceReconnect(),
    isConnected: () => this.healthWSApi.getConnectionState() === 'connected'
  });

  this.wsManager.registerConnection({
    name: 'tags',
    reconnectFunction: () => this.reconnectTags(),
    isConnected: () => this.websockets.tags?.readyState === WebSocket.OPEN
  });

  this.wsManager.registerConnection({
    name: 'nodes',
    reconnectFunction: () => this.reconnectNodes(),
    isConnected: () => this.websockets.nodes?.readyState === WebSocket.OPEN
  });

  this.wsManager.registerConnection({
    name: 'logs',
    reconnectFunction: () => this.reconnectLogs(),
    isConnected: () => this.websockets.logs?.readyState === WebSocket.OPEN
  });

  this.wsManager.registerConnection({
    name: 'broadcast',
    reconnectFunction: () => this.broadcastEventWSApi.forceReconnect(),
    isConnected: () => this.broadcastEventWSApi.getConnectionState() === 'connected'
  });
}
```

### 4. Set Up Master Heartbeat Coordination

```typescript
private initializeConnections(): void {
  // Set up health as master coordinator
  this.healthWSApi.setConnectionLostCallback(() => {
    console.log('Health connection lost, triggering full reconnection');
    this.wsManager.reconnectAllConnections();
  });

  // Set up other APIs to trigger coordinated reconnection
  this.broadcastEventWSApi.setConnectionLostCallback(() => {
    this.wsManager.reconnectAllConnections();
  });

  this.tagsWSApi.setConnectionLostCallback(() => {
    this.wsManager.reconnectAllConnections();
  });

  this.nodesWSApi.setConnectionLostCallback(() => {
    this.wsManager.reconnectAllConnections();
  });

  this.logWSApi.setConnectionLostCallback(() => {
    this.wsManager.reconnectAllConnections();
  });

  // Initialize connections
  this.connectHealth();
  this.connectBroadcastEvents();
  this.connectTags();
  this.connectNodes();
  this.connectLogs();
}
```

### 5. Connection Methods

```typescript
private connectHealth(): void {
  const healthSub = this.healthWSApi.getHealthData().subscribe(
    data => {
      // Handle health data
      console.log('Health data received:', data);
    },
    error => {
      console.error('Health WebSocket error:', error);
    }
  );
  this.subscriptions.push(healthSub);
}

private connectBroadcastEvents(): void {
  const broadcastSub = this.broadcastEventWSApi.getBroadcastEventData().subscribe(
    data => {
      // Handle broadcast event data
      console.log('Broadcast event received:', data);
    },
    error => {
      console.error('Broadcast WebSocket error:', error);
    }
  );
  this.subscriptions.push(broadcastSub);
}

private connectTags(): void {
  const tagsSub = this.tagsWSApi.openWebsocket().subscribe(
    websocket => {
      this.websockets.tags = websocket;
      
      const tagsDataSub = this.tagsWSApi.getTagsData(websocket).subscribe(
        data => {
          // Handle tags data
          console.log('Tags data received:', data);
        },
        error => {
          console.error('Tags data error:', error);
        }
      );
      this.subscriptions.push(tagsDataSub);
    },
    error => {
      console.error('Tags WebSocket connection failed:', error);
    }
  );
  this.subscriptions.push(tagsSub);
}

private connectNodes(): void {
  const nodesSub = this.nodesWSApi.openWebsocket().subscribe(
    websocket => {
      this.websockets.nodes = websocket;
      
      const nodesDataSub = this.nodesWSApi.getNodesData(websocket).subscribe(
        data => {
          // Handle nodes data
          console.log('Nodes data received:', data);
        },
        error => {
          console.error('Nodes data error:', error);
        }
      );
      this.subscriptions.push(nodesDataSub);
    },
    error => {
      console.error('Nodes WebSocket connection failed:', error);
    }
  );
  this.subscriptions.push(nodesSub);
}

private connectLogs(): void {
  const logsSub = this.logWSApi.openWebsocket().subscribe(
    websocket => {
      this.websockets.logs = websocket;
      
      const logsDataSub = this.logWSApi.getLogData(websocket).subscribe(
        data => {
          // Handle log data
          console.log('Log data received:', data);
        },
        error => {
          console.error('Log data error:', error);
        }
      );
      this.subscriptions.push(logsDataSub);
    },
    error => {
      console.error('Log WebSocket connection failed:', error);
    }
  );
  this.subscriptions.push(logsSub);
}
```

### 6. Reconnection Methods

```typescript
private reconnectTags(): void {
  if (this.websockets.tags) {
    this.websockets.tags.close();
  }
  setTimeout(() => this.connectTags(), 1000);
}

private reconnectNodes(): void {
  if (this.websockets.nodes) {
    this.websockets.nodes.close();
  }
  setTimeout(() => this.connectNodes(), 1000);
}

private reconnectLogs(): void {
  if (this.websockets.logs) {
    this.websockets.logs.close();
  }
  setTimeout(() => this.connectLogs(), 1000);
}
```

### 7. Cleanup

```typescript
ngOnDestroy(): void {
  this.subscriptions.forEach(sub => sub.unsubscribe());
  
  // Unregister from manager
  this.wsManager.unregisterConnection('health');
  this.wsManager.unregisterConnection('tags');
  this.wsManager.unregisterConnection('nodes');
  this.wsManager.unregisterConnection('logs');
  this.wsManager.unregisterConnection('broadcast');
  
  // Close connections
  Object.values(this.websockets).forEach(ws => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.close();
    }
  });
  
  this.healthWSApi.close();
  this.broadcastEventWSApi.close();
  this.tagsWSApi.close();
  this.nodesWSApi.close();
  this.logWSApi.close();
}
```

## Connection State Monitoring

```typescript
// Monitor connection states
this.healthWSApi.connectionState$.subscribe(state => {
  console.log('Health connection state:', state);
});

this.broadcastEventWSApi.connectionState$.subscribe(state => {
  console.log('Broadcast connection state:', state);
});

// Monitor manager events
this.wsManager.reconnectionEvents$.subscribe(event => {
  console.log('WebSocket Manager event:', event);
});
```

## Manual Controls

```typescript
// Force reconnection of all WebSockets
public forceReconnectAll(): void {
  this.wsManager.reconnectAllConnections();
}

// Get connection status
public getConnectionStatus(): void {
  const status = this.wsManager.getConnectionStatus();
  console.log('Connection status:', status);
}

// Force reconnect specific connection
public forceReconnectSpecific(name: string): void {
  this.wsManager.reconnectConnection(name);
}
```

## Key Features

1. **Automatic Heartbeat Detection**: Each WebSocket monitors incoming messages
2. **Coordinated Reconnection**: When one connection fails, all reconnect together
3. **Staggered Delays**: Prevents server overload during reconnection
4. **Connection State Tracking**: Real-time status monitoring
5. **Manual Override**: Force reconnection when needed
6. **Centralized Management**: Single point of control for all WebSockets

## Configuration

Heartbeat settings can be adjusted in each API:
- `heartbeatInterval`: Expected time between messages (default: 30 seconds)
- `maxMissedHeartbeats`: Tolerance before reconnection (default: 3)
- `maxReconnectAttempts`: Maximum reconnection attempts (default: 5)
- `reconnectInterval`: Base delay between attempts (default: 3 seconds)
