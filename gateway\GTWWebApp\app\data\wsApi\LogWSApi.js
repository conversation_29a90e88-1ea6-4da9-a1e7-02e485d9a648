System.register(["@angular/core", "rxjs", "../configuration", "../../modules/alert/alert.service", "@ngx-translate/core", "rxjs/operators"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, rxjs_1, configuration_1, alert_service_1, core_2, operators_1, LogWSApi;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (rxjs_1_1) {
                rxjs_1 = rxjs_1_1;
            },
            function (configuration_1_1) {
                configuration_1 = configuration_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (operators_1_1) {
                operators_1 = operators_1_1;
            }
        ],
        execute: function () {
            LogWSApi = (function () {
                function LogWSApi(alertService, translate) {
                    this.alertService = alertService;
                    this.translate = translate;
                    this.basePath = "ws://localhost/rest";
                    this.configuration = new configuration_1.Configuration();
                    this.reconnectAttempts = 0;
                    this.maxReconnectAttempts = 5;
                    this.reconnectInterval = 3000;
                    this.lastHeartbeatTime = 0;
                    this.heartbeatInterval = 30000;
                    this.missedHeartbeats = 0;
                    this.maxMissedHeartbeats = 3;
                    this.connectionState = new rxjs_1.BehaviorSubject('disconnected');
                    this.connectionState$ = this.connectionState.asObservable();
                }
                LogWSApi.prototype.setConnectionLostCallback = function (callback) {
                    this.onConnectionLostCallback = callback;
                };
                LogWSApi.prototype.getConnectionState = function () {
                    return this.connectionState.value;
                };
                LogWSApi.prototype.forceReconnect = function () {
                    this.alertService.debug("Log WebSocket: Forcing reconnection");
                    this.close();
                    this.reconnectAttempts = 0;
                };
                LogWSApi.prototype.close = function () {
                    this.stopHeartbeatMonitoring();
                    if (this.currentWebSocket) {
                        this.currentWebSocket.close();
                        this.currentWebSocket = null;
                    }
                    this.connectionState.next('disconnected');
                };
                LogWSApi.prototype.openWebsocket = function () {
                    var _this = this;
                    var wsUrl = "";
                    if (location.protocol != 'https:')
                        wsUrl = "ws://" + window.location.host + "/getLogEntries?token=" + this.configuration.apiKeys["Authorization"];
                    else
                        wsUrl = "wss://" + window.location.host + "/getLogEntries?token=" + this.configuration.apiKeys["Authorization"];
                    var websocket = new WebSocket(wsUrl);
                    this.currentWebSocket = websocket;
                    return rxjs_1.Observable.create(function (observer) {
                        websocket.onopen = function (openEvent) {
                            _this.reconnectAttempts = 0;
                            _this.missedHeartbeats = 0;
                            _this.connectionState.next('connected');
                            _this.startHeartbeatMonitoring();
                            _this.translate.get("TR_WEBSOCKET_OPEN", { websocketName: "getLogEntries" }).subscribe(function (res) {
                                _this.alertService.debug(res);
                            });
                            observer.next(websocket);
                            observer.complete();
                        };
                        websocket.onerror = function (error) {
                            _this.stopHeartbeatMonitoring();
                            _this.connectionState.next('error');
                            observer.error(error);
                        };
                    });
                };
                LogWSApi.prototype.getLogData = function (websocket) {
                    var _this = this;
                    return rxjs_1.Observable.create(function (observer) {
                        websocket.onmessage = function (evt) {
                            _this.onHeartbeatReceived();
                            observer.next(evt);
                        };
                        websocket.onclose = function (evt) {
                            _this.stopHeartbeatMonitoring();
                            _this.connectionState.next('disconnected');
                            if (evt.reason === "panelClose") {
                                _this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getLogEntries", reason: "panelClose" }).subscribe(function (res) {
                                    _this.alertService.debug(res);
                                });
                            }
                            else {
                                _this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getLogEntries", reason: evt.type }).subscribe(function (res) {
                                    _this.alertService.debug(res);
                                });
                                _this.attemptReconnection(observer);
                            }
                            observer.complete();
                        };
                    })
                        .pipe(operators_1.share());
                };
                LogWSApi.prototype.startHeartbeatMonitoring = function () {
                    var _this = this;
                    this.lastHeartbeatTime = Date.now();
                    this.missedHeartbeats = 0;
                    this.heartbeatTimeoutId = setInterval(function () {
                        _this.checkHeartbeat();
                    }, 10000);
                };
                LogWSApi.prototype.stopHeartbeatMonitoring = function () {
                    if (this.heartbeatTimeoutId) {
                        clearInterval(this.heartbeatTimeoutId);
                        this.heartbeatTimeoutId = null;
                    }
                };
                LogWSApi.prototype.onHeartbeatReceived = function () {
                    this.lastHeartbeatTime = Date.now();
                    this.missedHeartbeats = 0;
                };
                LogWSApi.prototype.checkHeartbeat = function () {
                    var now = Date.now();
                    var timeSinceLastHeartbeat = now - this.lastHeartbeatTime;
                    if (timeSinceLastHeartbeat > this.heartbeatInterval) {
                        this.missedHeartbeats++;
                        this.alertService.debug("Log WebSocket: Missed heartbeat " + this.missedHeartbeats + "/" + this.maxMissedHeartbeats + " (" + timeSinceLastHeartbeat + "ms since last)");
                        if (this.missedHeartbeats >= this.maxMissedHeartbeats) {
                            this.alertService.debug("Log WebSocket: Max missed heartbeats reached, reconnecting self only");
                            this.connectionState.next('heartbeat_lost');
                            this.forceReconnect();
                        }
                    }
                };
                LogWSApi.prototype.attemptReconnection = function (observer) {
                    var _this = this;
                    this.reconnectAttempts++;
                    this.connectionState.next('reconnecting');
                    if (this.reconnectAttempts <= this.maxReconnectAttempts) {
                        setTimeout(function () {
                            _this.translate.get("TR_WEBSOCKET_REOPENING", { websocketName: "getLogEntries", reconnectAttempt: _this.reconnectAttempts }).subscribe(function (res) {
                                _this.alertService.debug(res);
                            });
                        }, this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1));
                    }
                    else {
                        this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getLogEntries", reason: "Reopen fail" }).subscribe(function (res) {
                            _this.alertService.debug(res);
                        });
                    }
                };
                LogWSApi = __decorate([
                    core_1.Injectable(),
                    __metadata("design:paramtypes", [alert_service_1.AlertService, core_2.TranslateService])
                ], LogWSApi);
                return LogWSApi;
            }());
            exports_1("LogWSApi", LogWSApi);
        }
    };
});
//# sourceMappingURL=LogWSApi.js.map