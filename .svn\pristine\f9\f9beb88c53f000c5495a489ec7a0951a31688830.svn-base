/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright(c) 1997 - 2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWMasterDataObject.cpp                                     */
/* DESCRIPTION:  Definitions of basic MDO class                              */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com(919) 870 - 6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 36 ***_NOBODY_*** "                                                     */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#include "stdafx.h"
#include "gateway/GTWOsUtils/GtwOsSync.h"
#include "gateway/GTWOsUtils/GtwOsDateTime.h"
#include "GTWOpcUaSlaveDataObject.h"
#include "GTWOpcSlaveDataObject.h"
#include "GTWMMBMultiPoint.h"

//#include "GTWWebSocketServer.h"

#if USE_OPC_44
#include "OpcServer.h"
#include "GTWOpcAeAddressSpaceRoot.h"
#include "GTWOpcAeAddressSpaceElement.h"
#endif
#include "GTW61850DataAttributeMDO.h"
#include "GTWTASE2DataAttributeMDO.h"
#include "GTWMMBMultiPoint.h"
#include "gateway/GTWWebLib/GtwBroadcastMessage.h"

#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif

static const char* ClassName = "GTWMasterDataObject";
static tmw::LeakDetector gcounter(ClassName);


typedef std::unique_lock<std::mutex> ScopedLock;

//std::vector<GTWMasterDataObject *> *GTWMasterDataObject::g_mdoArray = NULL;

#if 0
typedef void (*drawInterface_unbindMdoFN)(void *, void *);
typedef void (*drawInterface_UpdateAnimatedObjectCBFN)(void *, void *);
static void di_unbindMdo(void *pDrawObject, void *pMdo)
{
  // find the function in the DLL and call it
  HMODULE hLib =  LoadLibrary("DrawLib.dll");

  drawInterface_unbindMdoFN ProcAdd;
  ProcAdd = (drawInterface_unbindMdoFN) GetProcAddress(hLib, "drawInterface_unbindMdo"); 
  if (ProcAdd)
  {
    (ProcAdd)(pDrawObject, pMdo);
  }
}

static void di_UpdateAnimatedObjectCB(void *pMdo, void *pDrawObject)
{
  // find the function in the DLL and call it
  HMODULE hLib =  LoadLibrary("DrawLib.dll");

  drawInterface_UpdateAnimatedObjectCBFN ProcAdd;
  ProcAdd = (drawInterface_UpdateAnimatedObjectCBFN) GetProcAddress(hLib, "drawInterface_UpdateAnimatedObjectCB"); 
  if (ProcAdd)
  {
    (ProcAdd)(pMdo, pDrawObject);
  }
}
#endif

std::list<CStdString> GTWMasterDataObject::g_sMdoXrefList;
TMWTYPES_UINT GTWMasterDataObject::g_updateCount = 0;

std::unordered_map<std::string, GTWMasterDataObject *> GTWMasterDataObject::gtwusrtg_map;

/*****************************************************************************/

GTWMasterDataObject *GTWMasterDataObject::gtwusrtg_findMDO(CStdString &userTagName)
{
  CStdString searchTagName = userTagName;
  if (int prdPos = userTagName.ReverseFind('.'); prdPos >= 0)
  { // found a '.'
    searchTagName = userTagName.Right(userTagName.GetLength()-prdPos-1);
  }
  if (auto pair = gtwusrtg_map.find(searchTagName); pair != gtwusrtg_map.end())
  {
    return(pair->second);
  }
  return nullptr;
}

/*****************************************************************************/

bool GTWMasterDataObject::gtwusrtg_insertInMap(GTWMasterDataObject *pMdo, const CStdString& newName)
{
  CStdString errStr;
  GTWMasterDataObject *pMember = nullptr;

  if (newName.Find('.') >= 0)
  {
    LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_General, nullptr, "Can not Add '%s' User Tag name for '%s' ('.' not allowed)\n",newName.c_str(),pMdo->getBdo()->GetFullName().c_str());
    return false;
  }

	std::string sOldName = pMdo->getMdoUserTagName();
  if (sOldName.compare(newName) == 0) // new name is same as old
  {
    return true;
  }

  if (newName != "")
  {
    std::unordered_map<std::string, GTWMasterDataObject *>::iterator iter;
    if ((iter = gtwusrtg_map.find(newName)) == gtwusrtg_map.end()) // nothing with new name exists
    {
      if (gtwusrtg_map.find(sOldName.c_str()) != gtwusrtg_map.end()) // if in map with old name, change to new name
      {
        auto pair = gtwusrtg_map.find(sOldName.c_str());
        pMember = pair->second;
        assert(pMember == pMdo);
        gtwusrtg_removeFromMap(pMdo);
        gtwusrtg_map[newName] = pMdo;
        pMdo->m_sUserTagName = newName;
      }
      else
      {
//        gtwusrtg_map.SetAt(newName, pMdo);
        gtwusrtg_map[newName] = pMdo;
        pMdo->m_sUserTagName = newName;
      }
      GTWOpcUaSlaveDataObject* pUaSdo = pMdo->GetOpcUaSDO();
      if (pUaSdo)
      {
        pUaSdo->ModifyUaServerName(newName);
      }
      return true;
    }
    else // new Name already exists
    {
      pMember = iter->second;
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_General, nullptr, "Cannot set '%s' User Tag name for '%s' because it is already defined for '%s'\n", newName.c_str(),
              (const char *)pMdo->getBdo()->GetFullName(), (const char *)pMember->getBdo()->GetFullName());
      return false;
    }
#if 0
    if (bSuccess == TMWDEFS_FALSE && pMdo->getMdoUserTagName() == "")
    { // new item
      gtwusrtg_map.insert(std::pair<CStdString, GTWMasterDataObject *>( newName,pMdo));
      pMdo->m_sUserTagName = newName;
      return true;
    }
    else if (bSuccess == true && pMdo == pMember)
    { // replace
      gtwusrtg_removeFromMap(pMdo);
      gtwusrtg_map.insert(std::pair<CStdString, GTWMasterDataObject *>( newName,pMdo));
      pMdo->m_sUserTagName = newName;
      return true;
    }
    else
    {
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_General, nullptr, "Can not Add '%s' User Tag name for '%s' (already defined)\n",pMdo->getMdoUserTagName().c_str(),pMdo->getBdo()->GetFullName().c_str());
      return false;
    }
#endif
    //errStr.Format("Can not Add '%s' Tag to user tag (no user tagname specified)\n",pMdo->getBdo()->GetFullName().c_str());
  }
  else if (sOldName.length() > 0) // then remove from map
  {
    if (gtwusrtg_map.find(sOldName.c_str()) != gtwusrtg_map.end())
    {
      auto pair = gtwusrtg_map.find(sOldName.c_str());
      pMember = pair->second;
      assert(pMember == pMdo);
      gtwusrtg_removeFromMap(pMdo);
      pMdo->m_sUserTagName = newName;
      GTWOpcUaSlaveDataObject* pUaSdo = pMdo->GetOpcUaSDO();
      if (pUaSdo)
      {
        CStdString sn = pMdo->getBdo()->GetMemberName();
        pUaSdo->ModifyUaServerName(pMdo->getBdo()->GetMemberName());
      }
      return true;
    }
  }
  LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_General, nullptr, "Can not Add '%s' Tag to user tag (no user tagname specified)\n",(const char *)pMdo->getBdo()->GetFullName());
  return false;
}

/*****************************************************************************/

bool GTWMasterDataObject::gtwusrtg_removeFromMap(GTWMasterDataObject *pMdo)
{
  if (pMdo->getMdoUserTagName() == "")
  {
    return true;
  }

  bool bError = false;
  CStdString errStr;
  if (auto pair = gtwusrtg_map.find(pMdo->getMdoUserTagName()); pair != gtwusrtg_map.end())
  {
    if (!gtwusrtg_map.erase(pMdo->getMdoUserTagName()))
    {
      bError = true;
    }
  }
  else
  {
    bError = true;
  }
  if (bError)
  {
    LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_OPC, nullptr, "Can not Remove '%s' User Tag (not found)",pMdo->getMdoUserTagName().c_str());
    return false;
  }
  return true;
}

bool GTWMasterDataObject::IsInEquation()
{
  for (int i = 0; i < m_SdoReadBoundList.getSize(); i++)
  {
    GTWSlaveDataObject *pSDO = m_SdoReadBoundList[i];
    if (pSDO && dynamic_cast<GTWEquationDataObject*>(pSDO))
    {
      return true;
    }
  }

  return false;
}

void GTWMasterDataObject::GetDependentEquations(std::list<GTWEquationDataObject*>& list)
{
  for (int i = 0; i < m_SdoReadBoundList.getSize(); i++)
  {
    GTWSlaveDataObject* pSDO = m_SdoReadBoundList[i];
    GTWEquationDataObject* pEq = nullptr;
    if (pSDO && (pEq = dynamic_cast<GTWEquationDataObject*>(pSDO)))
    {
      list.push_back(pEq);
    }
  }
}

GTWEquationDataObject* GTWMasterDataObject::GetUniqueEquation()
{
  if (m_MdoWriteBoundList.getSize() > 0) // then this is mapped to at least one mdo that is not an equation
  {
    return nullptr;
  }

#ifdef _WIN32
  int maxSize = 3;
#else
  int maxSize = 2;
#endif

  // Now check if this MDO is inside an equation and nothing else
  if (m_SdoReadBoundList.getSize() > 0 && m_SdoReadBoundList.getSize() <= maxSize) // maxSize because it could be mapped to OPC server and/or OPCUA server
  {
    GTWEquationDataObject* pEquationDataObj = nullptr;
    int size = m_SdoReadBoundList.getSize();
    for (int i = 0; i < size; i++)
    {
      GTWSlaveDataObject* pSDO = m_SdoReadBoundList[i];
      if (pSDO)
      {
        GTWEquationDataObject* pEq = nullptr;
        if (pEq = dynamic_cast<GTWEquationDataObject*>(pSDO))
        {
          if (pEquationDataObj) // can only be one equation
          {
            return nullptr;
          }
          pEquationDataObj = pEq;
          continue;
        }
#ifdef _WIN32
#if USE_OPC_44
        else if (!dynamic_cast<GTWOpcSlaveDataObject*>(pSDO) && !dynamic_cast<GTWOpcUaSlaveDataObject*>(pSDO)) // if it is mapped to something other than OPC server or OPC UA server, then return false
        {
          return nullptr;
        }
#endif
#if USE_OPC_UA
        else if (!dynamic_cast<GTWOpcUaSlaveDataObject*>(pSDO)) // if it is mapped to something other than OPC server or OPC UA server, then return false
        {
          return nullptr;
        }
#endif
#else
        else if (!dynamic_cast<GTWOpcUaSlaveDataObject*>(pSDO)) // if it is mapped to something other than OPC UA server, then return false
        {
          return nullptr;
        }
#endif
      }
    }
    return pEquationDataObj;
  }
  return nullptr;
}

GTWEquationDataObject* GTWMasterDataObject::IsBoundToEquation(void)
{
  int size = m_MdoReadBoundList.getSize();
  for (int i = 0; i < size; i++)
  {
    GTWEquationDataObject* pEqDo = dynamic_cast<GTWEquationDataObject*>(m_MdoReadBoundList[i]);
    if (pEqDo)
    {
      return pEqDo;
    }
  }
  return nullptr;
}

CStdString GTWMasterDataObject::getBindErrorString(GTW_BIND_STATUS bindStatus)
{
  switch(bindStatus)
  {
  case GTW_BIND_STATUS_FAILED:
    return "Binding Failed";
    break;
  case GTW_BIND_STATUS_DUPLICATE:
    return "Duplicate Binding";
    break;
  case GTW_BIND_STATUS_NOT_COMMAND:
    return "Not a command or writeable MDO";
    break;
  case GTW_BIND_STATUS_NO_TYPE_CONVERSION:
    return "Type conversion not supported";
    break;
  case GTW_BIND_STATUS_SUCCESS:
    return "Success";
    break;
  }
  return "Undefined Error";
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::GTWMasterDataObject
	Description :[none]	
	Return :			constructor	-	
	Parameters :
			void	-	
	Note :[none]
\**********************************************************************************/
GTWMasterDataObject::GTWMasterDataObject(void) :
  m_iOpcUaSeverity(180),
  m_sOpcUaMessage("Value has changed"),
  m_stdQuality(GTWDEFS_STD_QLTY_UNINITIALIZED),
#if USE_OPC_44
  m_eOPCReportingMode((GTWDEFS_OPC_REPORT_MODE)(int)GTWConfig::OPCServerReportMode),
#endif
  //m_bOPCTagModeSpecified(TMWDEFS_FALSE),
  m_bOPCReportingModeSpecified(TMWDEFS_FALSE),
#if USE_OPC_44
  m_eOPCTimeSource((GTWDEFS_TIME_SOURCE)(int)GTWConfig::OPCTimeSource),
  m_eOPCAETimeSource((GTWDEFS_TIME_SOURCE)(int)GTWConfig::OPCAETimeSource),
#endif
  m_bOPCTimeSourceSpecified(TMWDEFS_FALSE),
  m_bOPCAETimeSourceSpecified(TMWDEFS_FALSE),
  m_bOPCAESimpleEventSpecified(TMWDEFS_FALSE),
  m_bOPCAEUseValueAsMessage(TMWDEFS_FALSE),
  m_bAllowCommand(TMWDEFS_FALSE),
#if USE_OPC_44
  //m_eOPCTagMode((GTWDEFS_OPC_TAG_MODE)(int)GTWConfig::OPCServerTagMode),
#endif
  m_bScaleSpecified(TMWDEFS_FALSE),
  m_bOPCUAEventSpecified(TMWDEFS_FALSE),
  m_bOPCUAPublish(true),
  m_OPCAESeverity(345),
  m_OPCAEInActiveSeverity(145),
  m_sAEMessage("Change of State"),
  m_sAEConditionName("COS"),
  m_sAEInActiveConditionName("COS"),
  m_sAEAlarmValue(""),
  m_subCondName(""),
  m_subCondInActiveName(""),
  m_condName(""),
  m_condInActiveName(""),
  m_bSaveThisMdo(true),
  m_bAlarmAcked(false),
  m_bOPCsubscribed(TMWDEFS_FALSE),
  m_pEventLogFile(TMWDEFS_NULL),
  m_iLogFileUpdtrsnMask(0),
  m_iOpcAEUpdtrsnMask(0),
  m_isDnp(TMWDEFS_FALSE),
  m_bIsODBCMdo(false),
  m_bIsUpdating(false)
{
  gcounter.Add(this);
  m_vValue = 0;
  //if (g_mdoArray == NULL)
  //{
  //  g_mdoArray = new std::vector<GTWMasterDataObject *>();
  //}
  //g_mdoArray->push_back(this);

  m_dScaleRawMin=0.0;
  m_dScaleRawMax=0.0;
  m_dScaleEguMin=0.0;
  m_dScaleEguMax=0.0;
  
  m_sUserTagName = "";
  m_sDescriptionString = "";


  m_pBdo = NULL;
  m_pVariantReadCnvtr = TMWDEFS_NULL;

  //m_pMdoWriteTimerInfo = nullptr;
  //m_pMdoWriteTimer = nullptr;

#if USE_OPC_44
  m_bIsSubCond = false;
  m_bIsInActiveSubCond = false;
  m_bOPCAEFireCondOnAttrChange = false;

  m_pCategoryActive = NULL;
  m_pCategoryInActive = NULL;
  m_activeOrNotAcknowledgedCond = NULL;
  //EnumConditionChange_ACTIVE_STATE 0x0001|
  //EnumConditionChange_MESSAGE 0x0040|
  //EnumConditionChange_SEVERITY 0x0010|
  //EnumConditionChange_QUALITY  0x0008|
  //EnumConditionChange_ATTRIBUTE 0x0080|
  //EnumConditionChange_SUBCONDITION 0x0020
  m_iOPCAE_ACTMASK = 0x0001 | 0x0040 | 0x0010 | 0x0008 | 0x0080 | 0x0020;   // 


  //EnumConditionChange_ACTIVE_STATE 0x0001|
  //EnumConditionChange_MESSAGE 0x0040|
  //EnumConditionChange_SEVERITY 0x0010|
  //EnumConditionChange_QUALITY  0x0008|
  //EnumConditionChange_ATTRIBUTE 0x0080|
  //EnumConditionChange_SUBCONDITION 0x0020
  m_iOPCAE_INACTMASK = 0x0001 | 0x0040 | 0x0010 | 0x0008 | 0x0080 | 0x0020; // 

  m_bOPCAEActiveAckRequired = true;
  m_bOPCAEInActiveAckRequired = false;
  m_iOPCAE_ACTSTATE = EnumConditionState_ACTIVE;
  m_iOPCAE_INACTSTATE = EnumConditionState_NO;
#endif


#if USE_OPC_44
  m_bOPCPublish = GTWConfig::OPCNamespacePublishMode == OPC_NAMESPACE_PUBLISH_MODE_ALL;
#endif
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::~GTWMasterDataObject
	Description :[none]	
	Return :			destructor	-	
	Parameters :
	Note :[none]
\**********************************************************************************/
GTWMasterDataObject::~GTWMasterDataObject()
{
  gcounter.Remove(this);
  try
  {
   // size_t iThis;
   // int i;
	  //for (iThis = 0; iThis<g_mdoArray->size();iThis++)
	  //{
	  //  if (g_mdoArray->at(iThis) == this)
	  //  {
	  //    break;
	  //  }
	  //}
	  //if (g_mdoArray->size() != 0)
	  //{
		 // g_mdoArray->erase(g_mdoArray->begin() + iThis);
	  //}
	
    //if (m_pMdoWriteTimer)
    //{
    //  m_pMdoWriteTimer->KillTimer();
    //}

    //if (m_pMdoWriteTimerInfo)
    //{
    //  delete m_pMdoWriteTimerInfo;
    //  m_pMdoWriteTimerInfo = NULL;
    //}
    //if (m_pMdoWriteTimer)
    //{
    //  delete m_pMdoWriteTimer;
    //  m_pMdoWriteTimer = NULL;
    //}
	
#if 0
	  for (i = m_DrawObjectBoundList.getSize() - 1; i >= 0; i--)
	  {
	    void *pDrawObject = m_DrawObjectBoundList[i];
	    di_unbindMdo(pDrawObject,this);
	    m_DrawObjectBoundList.removeAt(i);
	  }
#endif
    tmw::Array<GTWBaseDataObject *> deleteList;

    int i = 0;
	  for (i = m_SdoReadBoundList.getSize() - 1; i >= 0; i--)
	  {
	    GTWSlaveDataObject *pSdo = m_SdoReadBoundList[i];
	    pSdo->unbindSdo(this);
	    m_SdoReadBoundList.removeAt(i);
	    GTWBaseDataObject *pBdo = pSdo->getBdo();
	    if (pBdo)
	    {
        if (deleteList.find(pBdo) == -1)
          deleteList.add(pBdo);
	    }
	  }
	  
    // uncommented to fix CR Number:	20965
	  for (i = m_MdoReadBoundList.getSize() - 1; i >= 0; i--)
	  {
	    GTWMasterDataObject *pMdo = m_MdoReadBoundList[i];
	    pMdo->unbindMdo(this);
	    m_MdoReadBoundList.removeAt(i);
	  }

	  for (i = m_SdoWriteBoundList.getSize() - 1; i >= 0; i--)
	  {
	    GTWSlaveDataObject *pSdo = m_SdoWriteBoundList[i];
	    pSdo->unbindSdo(this);
	    m_SdoWriteBoundList.removeAt(i);
	    GTWBaseDataObject *pBdo = pSdo->getBdo();
	    if (pBdo)
	    {
        if (deleteList.find(pBdo) == -1)
          deleteList.add(pBdo);
      }
	  }
    for (auto ele : deleteList)
    {
#if USE_OPC_44
      if (ele->IsA("GTWOpcSlaveDataObject"))
        dynamic_cast<GTWOpcSlaveDataObject *>(ele)->CleanUpDAAddressSpace();
#endif

      ele->DeleteCollectionMember();
    }

	
	  for (i = m_MdoWriteBoundList.getSize() - 1; i >= 0; i--)
	  {
	    GTWMasterDataObject *pMdo = m_MdoWriteBoundList[i];
	    pMdo->unbindMdo(this);
	    m_MdoWriteBoundList.removeAt(i);
	  }
	
	  for (i = m_MdoReadBoundList.getSize() - 1; i >= 0; i--)
	  {
	    GTWMasterDataObject *pMdo = m_MdoReadBoundList[i];
	    for (int ii = pMdo->m_MdoWriteBoundList.getSize() - 1; ii >= 0; ii--)
	    {
	      GTWMasterDataObject *pMappedMdo = pMdo->m_MdoWriteBoundList[ii];
	      pMappedMdo->unbindMdo(this);
	      pMdo->m_MdoWriteBoundList.removeAt(ii);
	    }
	    
	    pMdo->unbindMdo(this);
	    m_MdoReadBoundList.removeAt(i);
	  }

#if USE_OPC_44

    //delete m_activeOrNotAcknowledgedCond;
    m_activeOrNotAcknowledgedCond = nullptr; // these do not need to be deleted - Softing library will delete it when terminate is called.

#endif
    gtwusrtg_removeFromMap(this);

    delete m_pVariantReadCnvtr;
    m_pVariantReadCnvtr = NULL;
  }
  catch (...)
  {
  	
  }

}

/**********************************************************************************\
	Function :			GTWMasterDataObject::addSdoToReadBoundList
	Description :[none]	
	Return :			void	-	
	Parameters :
			GTWSlaveDataObject *pUpdateSdo	-	
	Note :[none]
\**********************************************************************************/
void GTWMasterDataObject::addSdoToReadBoundList(GTWSlaveDataObject *pUpdateSdo)
{
  if (pUpdateSdo)
  {
    for (int i = m_SdoReadBoundList.getSize() - 1; i >= 0; i--)
    {
      if (m_SdoReadBoundList[i] == pUpdateSdo)
      {
        return;
      }
    }
    
    if (CStdString sn = pUpdateSdo->gtw_getFullName(); sn == GTWEquationDataObject::getValidateEquationName()) // we do not add this equation sdo to the readbound list because it is temporary and about to be deleted
    {
      return;
    }

    m_SdoReadBoundList.appendMembers(pUpdateSdo);
    pUpdateSdo->BindMdoToSdoForRead(this);
  }
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::addMdoToReadBoundList
	Description :[none]	
	Return :			void	-	
	Parameters :
			GTWMasterDataObject *pUpdateMdo	-	
	Note :[none]
\**********************************************************************************/
void GTWMasterDataObject::addMdoToReadBoundList(GTWMasterDataObject *pUpdateMdo)
{
#if 0
  if (pUpdateMdo)
  {
    int i;
    for (i = m_MdoReadBoundList.getSize() - 1; i >= 0; i--)
    {
      if (m_MdoReadBoundList[i] == pUpdateMdo) 
        return;
    }
    
    m_MdoReadBoundList.appendMembers(pUpdateMdo);
    pUpdateMdo->addMdoToMdoReadBoundList(this);
  }
#endif
}

#if 0
/**********************************************************************************\
	Function :			GTWMasterDataObject::addDrawObjectToUpdateList
	Description : [none]	
	Return :			void	-	
	Parameters :
			void *pDrawObj	-	
	Note : [none]
\**********************************************************************************/
void GTWMasterDataObject::addDrawObjectToUpdateList(void *pDrawObj)
{
  if (pDrawObj)
  {
    int i;
    for (i = m_DrawObjectBoundList.getSize() - 1; i >= 0; i--)
    {
      if (m_DrawObjectBoundList[i] == pDrawObj) 
        return;
    }
    // only bind of not already bound
    m_DrawObjectBoundList.appendMembers(pDrawObj);
  }
}
void GTWMasterDataObject::removeDrawObjectFromUpdateList(void *pDrawObj)
{
  if (pDrawObj)
  {
    int i;
    for (i = m_DrawObjectBoundList.getSize() - 1; i >= 0; i--)
    {
      if (m_DrawObjectBoundList[i] == pDrawObj) 
      {
        // only bind of not already bound
        m_DrawObjectBoundList.removeAt(i);
        return;
      }
    }
  }
}
#endif

/**********************************************************************************\
	Function :			GTWMasterDataObject::addSdoToWriteBoundList
	Description :[none]	
	Return :			void	-	
	Parameters :
			GTWSlaveDataObject *pWriteFromSdo	-	
	Note :[none]
\**********************************************************************************/
void GTWMasterDataObject::addSdoToWriteBoundList(GTWSlaveDataObject *pWriteFromSdo)
{
  if (pWriteFromSdo)
  {
    for (int i = m_SdoWriteBoundList.getSize() - 1;i >= 0;i--)
    {
      if (m_SdoWriteBoundList[i] == pWriteFromSdo) 
        return;
    }
    
    m_SdoWriteBoundList.appendMembers(pWriteFromSdo);
    pWriteFromSdo->BindMdoToSdoForWrite(this);
  }
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::addMdoToWriteBoundList
	Description :[none]	
	Return :			void	-	
	Parameters :
			GTWMasterDataObject *pWriteFromMdo	-	
	Note :[none]
\**********************************************************************************/
void GTWMasterDataObject::addMdoToWriteBoundList(GTWMasterDataObject *pWriteToMdo)
{
  if (pWriteToMdo)
  {
    for (int i = m_MdoWriteBoundList.getSize() - 1;i >= 0;i--)
    {
      if (m_MdoWriteBoundList[i] == pWriteToMdo)
      {
        pWriteToMdo->addMdoToMdoReadBoundList(this);
        return;
      }
    }
    
    m_MdoWriteBoundList.appendMembers(pWriteToMdo);
    pWriteToMdo->addMdoToMdoReadBoundList(this);
  }
}

void GTWMasterDataObject::removeMdoFromWriteBoundList(GTWMasterDataObject *pMdo)
{
  if (pMdo)
  {
    for (int i = m_MdoWriteBoundList.getSize() - 1; i >= 0; i--)
    {
      if (m_MdoWriteBoundList[i] == pMdo)
      {
        m_MdoWriteBoundList.removeAt(i);
        return;
      }
    }
  }
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::addMdoToMdoReadBoundList(
	Description :
                indicates the MDO has been bound to   
                the MDO, which may be used to build 
                a list of MDOs bound to the MDO.    

	Return :			void	-	
	Parameters :
			GTWMasterDataObject *pMdo	-	
	Note : [none]
\**********************************************************************************/
void GTWMasterDataObject::addMdoToMdoReadBoundList( GTWMasterDataObject *pMdo)
{
  // only allow one binding
  // MDOs can only receive data from one other MDO
  // so replace if already bound
  if (m_MdoReadBoundList.getSize() == 0)
  {
    m_MdoReadBoundList.appendMembers(pMdo);
  }
  else
  {
    m_MdoReadBoundList[0] = pMdo;
  }
}

void GTWMasterDataObject::updateMDOCommon(GTWDEFS_UPDTRSN updateReason)
{
  GtwOsSyncInc(&g_updateCount);
  this->setBdo(dynamic_cast<GTWBaseDataObject*>(this));

  if ((bool)GTWConfig::UseReportedTime == true && updateReason == GTWDEFS_UPDTRSN_UNKNOWN)
  {
    // do not set the update time
  }
  else
  {
    GTWBaseDataObject* pBdo = this->getBdo();
    if (pBdo != NULL && dynamic_cast<GTW61850DataAttributeMDO*>(pBdo) != nullptr)//  pBdo->IsA("GTW61850DataAttributeMDO"))
    {
      // set by other code
    }
    else if (pBdo != NULL && dynamic_cast<GTWTASE2DataAttributeMDO*>(pBdo) != nullptr)//pBdo->IsA("GTWTASE2DataAttributeMDO"))
    {
      // set by other code
    }
    else
    {
      if (pBdo != NULL)
      {
        pBdo->storeUpdatedTime(NULL);
        //GTWConfig::getSystemDateTime(&pBdo->m_dsUpdatedTime);
      }
    }
  }

  // If the update reason is allowed by the mask, then log it!
  if (m_pEventLogFile != nullptr && (updateReason & m_iLogFileUpdtrsnMask) == updateReason && updateReason != GTWDEFS_UPDTRSN_NONE)
  {
    GetGTWApp()->incrementSOECounter();
    m_pEventLogFile = m_pEventLogFile->writeEvent(this, updateReason);
  }

  /* If the update reason is allowed by the mask, then report it! */
  if (((updateReason & m_iOpcAEUpdtrsnMask) == updateReason)
    || (getOPCsubscribed() && ((GTWConfig::OPCDASubscriptionOpcAELogMask & updateReason) == updateReason))
    && updateReason != GTWDEFS_UPDTRSN_NONE)
  {
#if USE_OPC_44
    if ((isOPCAESimpleEventSpecified() == true) || ((bool)GTWConfig::OPCAEUseSimpleEvents == true))
    {
      reportOPCSimpleEvent(updateReason);
    }
    else
    {
      reportOPCConditionEvent(updateReason, false);
    }
#endif
  }
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::updateMDO
	Description : called when data is stored in the MDO	
	Return :			void	-	
	Parameters :
			GTWDEFS_UPDTRSN updateReason	-	
	Note :[none]
\**********************************************************************************/
void GTWMasterDataObject::updateMDO(GTWDEFS_UPDTRSN updateReason) 
{
  _updateMDO(updateReason, true);
}

void GTWMasterDataObject::updateNonWriteableMDOs(GTWDEFS_UPDTRSN updateReason)
{
  _updateMDO(updateReason, false);
}

void GTWMasterDataObject::_updateMDO(GTWDEFS_UPDTRSN updateReason, bool bUpdateWriteableMDOs)
{
  // Prevent circular reference updates by checking if this MDO is already being updated
  if (m_bIsUpdating)
  {
    return;
  }

  // Set the updating flag to prevent re-entry
  m_bIsUpdating = true;

  // RAII guard to ensure the flag is cleared even if an exception is thrown
  struct UpdateGuard {
    bool& flag;
    UpdateGuard(bool& f) : flag(f) {}
    ~UpdateGuard() { flag = false; }
  } guard(m_bIsUpdating);

  updateMDOCommon(updateReason);

  int i;
  for (i = m_SdoReadBoundList.getSize() - 1; i >= 0; i--)
  {
    m_SdoReadBoundList[i]->updateSDO(updateReason, this);
    GetGTWApp()->UpdateWsTag(m_SdoReadBoundList[i]->getBdo());
  }

//  for (i = m_MdoReadBoundList.getSize() - 1; i >= 0; i--)
//  {
//    m_MdoReadBoundList[i]->updateMDO(updateReason, this);
//  }

  for (i = m_MdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    // Sometimes updating writeable or command MDOs is not desireable when, for example, first connecting an OPC client to an OPC Server
    // customers can use the ini param OPCDelayExecuteControlOnConnect to delay writing mapped points on a connect
    if (!bUpdateWriteableMDOs && m_MdoWriteBoundList[i]->isWriteable())
    {
      continue;
    }

    m_MdoWriteBoundList[i]->updateMDO(updateReason, this);
  }

#if 0
  for (i = m_DrawObjectBoundList.getSize() - 1; i >= 0; i--)
  {
    di_UpdateAnimatedObjectCB(this, m_DrawObjectBoundList[i]);
  }
#endif
  GetGTWApp()->UpdateWsTag(this->getBdo());
}

void GTWMasterDataObject::updateOPCMappings(GTWDEFS_UPDTRSN updateReason)
{
  updateMDOCommon(updateReason);

  for (int i = m_SdoReadBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWSlaveDataObject* pSdo = m_SdoReadBoundList[i];
    // If it is an opcSDO, then update
#ifdef _WIN32
#if USE_OPC_44 && USE_OPC_UA
    if (dynamic_cast<GTWOpcSlaveDataObject*>(pSdo) || dynamic_cast<GTWOpcUaSlaveDataObject*>(pSdo))
    {
      pSdo->updateSDO(updateReason, this);
      GetGTWApp()->UpdateWsTag(m_SdoReadBoundList[i]->getBdo());
    }
#endif
#if USE_OPC_UA
    if (dynamic_cast<GTWOpcUaSlaveDataObject*>(pSdo))
    {
      pSdo->updateSDO(updateReason, this);
      GetGTWApp()->UpdateWsTag(m_SdoReadBoundList[i]->getBdo());
    }
#endif
#else
    if (dynamic_cast<GTWOpcUaSlaveDataObject*>(pSdo))
    {
      pSdo->updateSDO(updateReason, this);
      GetGTWApp()->UpdateWsTag(m_SdoReadBoundList[i]->getBdo());
    }
#endif
  }

  GetGTWApp()->UpdateWsTag(this->getBdo());
}

/**********************************************************************************\
	Function :			GTWMlaveDataObject::updateMDO
	Description : [none]	
	Return :			void	-	
	Parameters :
			GTWDEFS_UPDTRSN updateReason	-	
			GTWMasterDataObject *pMdo	-	
	Note : [none]
\**********************************************************************************/
void GTWMasterDataObject::updateMDO(GTWDEFS_UPDTRSN updateReason, GTWMasterDataObject *pMdo)
{
  /* do nothing by default */
}

void GTWMasterDataObject::updateOPCSDOs()
{
  for (int i = m_SdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWSlaveDataObject *pSdo = m_SdoWriteBoundList[i];
#if USE_OPC_44
    if (dynamic_cast<GTWOpcSlaveDataObject*>(pSdo))
    {
      m_SdoWriteBoundList[i]->updateSDO(GTWDEFS_UPDTRSN_REFRESH, this);
    }
#endif
#if USE_OPC_UA
    if (dynamic_cast<GTWOpcUaSlaveDataObject*>(pSdo))
    {
      m_SdoWriteBoundList[i]->updateSDO(GTWDEFS_UPDTRSN_REFRESH, this);
    }
#endif
  }
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::setMdoUserTagName
	Description :[none]	
	Return :			bool -	
	Parameters :
			const char *pUserTagName	-	
	Note :[none]
\**********************************************************************************/
bool GTWMasterDataObject::setMdoUserTagName(const char *userTagName)
{
  if (gtwusrtg_insertInMap(this,userTagName) == false) // will set m_sUserTagName
  {
    return false;
  }
  return true;
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::getParenUserTagName
	Description : This function returns the user tag name in parenthesis if it exists	
	Return :			CStdString	-	
	Parameters :
			void	-	
	Note :[none]
\**********************************************************************************/
CStdString GTWMasterDataObject::getParenUserTagName(void)
{
  if (m_sUserTagName != "")
  {
    return " (" + m_sUserTagName + ")";
  }
  else
  {
    return ("");
  }
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::bindMdoToSdoForReading
	Description :[none]	
	Return :			void *	-	
	Parameters :
			GTWSlaveDataObject *pUpdateSdo	-	
			GTWCNVTR_TYPE cnvtrType	-	
	Note :[none]
\**********************************************************************************/
void *GTWMasterDataObject::bindMdoToSdoForReading(GTWSlaveDataObject *pUpdateSdo, GTWCNVTR_TYPE cnvtrType)
{
  return (TMWDEFS_NULL);
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::bindMdoReportedTime
	Description :[none]	
	Return :			void *	-	
	Parameters :
			GTWCNVTR_TYPE cnvtrType	-	
	Note :[none]
\**********************************************************************************/
void *GTWMasterDataObject::bindMdoReportedTime(GTWCNVTR_TYPE cnvtrType)
{
  if (cnvtrType == GTWCNVTR_TYPE_TMWDTIME)
  {
    return (new GTWMasterDataObjectTimeConverter(this));
  } 
  
  return (TMWDEFS_NULL);
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::bindMdoToSdoForWriting
	Description :[none]	
	Return :			void *	-	
	Parameters :
			GTWSlaveDataObject *pWriteFromSdo	-	
			GTWCNVTR_TYPE cnvtrType	-	
	Note :[none]
\**********************************************************************************/
void *GTWMasterDataObject::bindMdoToSdoForWriting(GTWSlaveDataObject *pWriteFromSdo, GTWCNVTR_TYPE cnvtrType)
{
  return (TMWDEFS_NULL);
}

GTW_BIND_STATUS GTWMasterDataObject::bindMdoToMdoForWriting(GTWMasterDataObject *pWriteToMdo, GTWCNVTR_TYPE cnvtrType, GTWMdoVariantReadConverter **pConverter)
{
  *pConverter = TMWDEFS_NULL;

  if (!pWriteToMdo->isCommandMDO() && !pWriteToMdo->isWriteable())
  {
    LOG(GtwLogger::Severity_Warning, GtwLogger::SDG_Category_General, nullptr, "Warning: the mapping '%s' to '%s' is invalid because the MDO '%s' cannot be written. The SDG will try to map the reverse order...",
      this->getBdo()->GetFullName().c_str(), pWriteToMdo->getBdo()->GetFullName().c_str(),  pWriteToMdo->getBdo()->GetFullName().c_str());

    return GTW_BIND_STATUS_NOT_COMMAND;
  }

  if (GTWCNVTR_TYPE thisCnvtrType = getCnvtrType(); cnvtrType != thisCnvtrType && pWriteToMdo->checkMdoToMdoConverter(thisCnvtrType) != true)
  {
    return GTW_BIND_STATUS_NO_TYPE_CONVERSION;
  }

  *pConverter = new GTWMDO_CNVTR_READ_VARIANT(this); 

  if (*pConverter)
  {
    addMdoToWriteBoundList(pWriteToMdo);
  }

  return GTW_BIND_STATUS_SUCCESS;
}

GTW_BIND_STATUS GTWMasterDataObject::bindMdoToMdoForReading(GTWMasterDataObject *pSourceMdo, GTW_BIND_MODE bindMode)
{
  GTWMasterDataObject *pCurBoundMdo = TMWDEFS_NULL;
  if (m_MdoReadBoundList.getSize() > 0) // then this MDO already has a source MDO that is writing to this
  {
    GTWMasterDataObject* pThis = const_cast<GTWMasterDataObject*>(this);
    if (GTWMMBMultiPoint *pDualReg = dynamic_cast<GTWMMBMultiPoint*>(pThis))
    {
      if (m_MdoReadBoundList.getSize() == 3)
      {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_General, nullptr, "The DualRegister %s is already mapped. Please delete existing mapping and retry.", pDualReg->GetFullName().c_str());
        return GTW_BIND_STATUS_FAILED;
      }
    }
    else
    {
      pCurBoundMdo = m_MdoReadBoundList[0];
      if (bindMode != GTW_BIND_MODE_ALLOW_REPLACE) // then we do not allow replace and fail the binding
      {
        //pCurBoundMdo = m_MdoReadBoundListundList[0];

        // Send broadcast message to web UI (this also handles backend logging automatically)
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_General, nullptr,
          "TR_ERROR_MDO_MAPPING_CONFLICT", "Failed to map MDO '{{arg1}}' to MDO '{{arg2}}' because it is already mapped to '{{arg3}}'.",
          (const char*)pSourceMdo->getBdo()->GetFullName(), (const char*)this->getBdo()->GetFullName(), (const char*)pCurBoundMdo->getBdo()->GetFullName());

        return GTW_BIND_STATUS_FAILED;
      }
      pCurBoundMdo = m_MdoReadBoundList[0]; // current source MDO that is writing to this MDO which we are attempting to replace
    }
  }

  GTWMdoVariantReadConverter *pReadCnvtr = nullptr;
  GTW_BIND_STATUS bindStatus = GTW_BIND_STATUS_FAILED;
  if (pSourceMdo != nullptr)
  {
    const GTWCNVTR_TYPE targetCnvtrType = getCnvtrType();
    bindStatus = pSourceMdo->bindMdoToMdoForWriting(this, targetCnvtrType, &pReadCnvtr);
	  if (bindStatus != GTW_BIND_STATUS_SUCCESS)
	  {
	    if (bindStatus ==  GTW_BIND_STATUS_NOT_COMMAND)
	    {
	      bindStatus = this->bindMdoToMdoForWriting(pSourceMdo, targetCnvtrType, &pReadCnvtr);
	    }
	    if (bindStatus != GTW_BIND_STATUS_SUCCESS)
	    {
	      return bindStatus;
	    }
	  }
  }

  GTWMasterDataObject *pNewBoundMdo = nullptr;
  if (m_MdoReadBoundList.getSize() > 0)
  {
    pNewBoundMdo = m_MdoReadBoundList[0];
  }

  if (bindStatus == GTW_BIND_STATUS_SUCCESS && pReadCnvtr == nullptr)
  {
    return GTW_BIND_STATUS_FAILED;
  }

  if (m_pVariantReadCnvtr == nullptr)
  {
    m_pVariantReadCnvtr = pReadCnvtr;
  }
  else if (pCurBoundMdo != nullptr && pNewBoundMdo != pCurBoundMdo) // so we have a new source MDO writing this MDO
  {
    pCurBoundMdo->unbindMdo(this); // Remove this MDO from the write bound list of Current Bound MDO

    LOG(GtwLogger::Severity_Warning, GtwLogger::SDG_Category_General, nullptr, "Warning: MDO '%s' has been rebound to MDO '%s'. Previous MDO binding was to '%s'.",
      (const char *)this->getBdo()->GetFullName(), (const char*)pNewBoundMdo->getBdo()->GetFullName(), (const char*)pCurBoundMdo->getBdo()->GetFullName());

    delete m_pVariantReadCnvtr;
    m_pVariantReadCnvtr = pReadCnvtr;
  }

  return GTW_BIND_STATUS_SUCCESS;
}

GTW_BIND_STATUS GTWMasterDataObject::bindMdoWithMdo(GTWMasterDataObject *pSourceMdo, GTW_BIND_MODE bindMode)
{
  GTW_BIND_STATUS bindStatus = GTW_BIND_STATUS_FAILED;
  GTWMasterDataObject* pDestMdo = this;
  bool bDoUpdate = false;

  GTWEquationDataObject* pEdo = dynamic_cast<GTWEquationDataObject*>(pSourceMdo);
  if (pEdo)
  {
    if (pEdo->isWritable()) // if an equation has been set to writable, we try to setup the mapping in both directions
    {
      bindStatus = pSourceMdo->bindMdoToMdoForReading(this, bindMode);
      if (bindStatus == GTW_BIND_STATUS_SUCCESS)
      {
        bindStatus = bindMdoToMdoForReading(pSourceMdo, bindMode);
        if (bindStatus == GTW_BIND_STATUS_SUCCESS)
        {
          pSourceMdo->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED, this);
        }
      }
    }
  }

  if (bindStatus != GTW_BIND_STATUS_SUCCESS)
  {
    /* need to put this in ??
    if (pSourceMdo->isControl())
    {
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_General, nullptr, "The mapping '%s' to '%s' is invalid because MDO '%s' is a control and cannot be a source.",
        pSourceMdo->getBdo()->GetFullName().c_str(), this->getBdo()->GetFullName().c_str(), this->getBdo()->GetFullName().c_str());
      return bindStatus;
    }

    */


    bindStatus = bindMdoToMdoForReading(pSourceMdo, bindMode);
  }

  if (bindStatus == GTW_BIND_STATUS_NO_TYPE_CONVERSION)
  {
    LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_General, nullptr, "The mapping '%s' to '%s' is invalid because MDO '%s' does not support the type conversion.",
      pSourceMdo->getBdo()->GetFullName().c_str(), this->getBdo()->GetFullName().c_str(), this->getBdo()->GetFullName().c_str());
    return bindStatus;
  }

  if (bindStatus == GTW_BIND_STATUS_NOT_COMMAND)
  {
    LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_General, nullptr, "The mapping '%s' to '%s' is invalid because MDO '%s' cannot be written. The SDG will try to map the reverse order...",
      pSourceMdo->getBdo()->GetFullName().c_str(), this->getBdo()->GetFullName().c_str(), this->getBdo()->GetFullName().c_str());
  }

  if (bindStatus != GTW_BIND_STATUS_SUCCESS && pSourceMdo != nullptr)
  {
    if (pSourceMdo->m_MdoReadBoundList.getSize() > 0)
    {
      // then source is already bound to an MDO,ie it is being written to by another MDO
      // so we fail at this point since we dont know if they want to rebind it being they tried to bind in the other direction

      GTWMasterDataObject *pCurBoundMdo = pSourceMdo->m_MdoReadBoundList[0];
      if (pCurBoundMdo != this)
      {
        // Send broadcast message to web UI (this also handles backend logging automatically)
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_General, nullptr,
          "TR_ERROR_MDO_MAPPING_CONFLICT", "Failed to map MDO '{{arg1}}' to MDO '{{arg2}}' because it is already mapped to '{{arg3}}'.",
          (const char*)pSourceMdo->getBdo()->GetFullName(), (const char*)this->getBdo()->GetFullName(), (const char*)pCurBoundMdo->getBdo()->GetFullName());

        return bindStatus;
      }
      else
      {
        return GTW_BIND_STATUS_SUCCESS;
      }
    }

    // Now try to bind in the other direction
    bindStatus = pSourceMdo->bindMdoToMdoForReading(this, bindMode);
    if (bindStatus != GTW_BIND_STATUS_SUCCESS)
    {
      return bindStatus;
    }
    pDestMdo = pSourceMdo; // new we have a new destination MDO since we successfully reversed the mapping order
  }

  if (!bDoUpdate && pDestMdo->getBdo())
  {
    if (pDestMdo->getBdo()->IsAinternalMDO()
      || pDestMdo->getBdo()->IsAuserMDO()
      || pDestMdo->getBdo()->IsA("GTWEquationDataObject")
      )
    {
      bDoUpdate = true;
    }
    else if (pSourceMdo != nullptr && pSourceMdo->getBdo() != nullptr)
    {
      if (pSourceMdo->getBdo()->IsAinternalMDO() || pSourceMdo->getBdo()->IsAuserMDO())
      {
        bDoUpdate = true;
      }
    }
  }
  if (bDoUpdate)
  {
    pDestMdo->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED, pSourceMdo);
  }

  return GTW_BIND_STATUS_SUCCESS;
}
/**********************************************************************************\
	Function :			GTWMasterDataObject::unbindAllMdos
	Description :[none]	
	Return :			void	-	
	Parameters :
			void	-	
	Note :[none]
\**********************************************************************************/
void GTWMasterDataObject::unbindAllForRemoval(void)
{
  //
  // Completely unbind this MDO from all mappings and clean up memory associated with removed SDOs
  //

  std::list<GTWBaseDataObject *> BDOListToDelete;
  int i;
  
#if 0
  for (i = m_DrawObjectBoundList.getSize() - 1; i >= 0; i--)
  {
    void *pDrawObject = m_DrawObjectBoundList[i];
    di_unbindMdo(pDrawObject,this);
    m_DrawObjectBoundList.removeAt(i);
  }
#endif
  
  for (i = m_SdoReadBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWSlaveDataObject *pSdo = m_SdoReadBoundList[i];
    pSdo->unbindSdo(this);
    if (GTWBaseDataObject *pSdoBdo = pSdo->getBdo())
    {
      if (pSdoBdo->GetParentCollection())
      {
        pSdoBdo->GetParentCollection()->RemoveCollectionMember(pSdoBdo);
      }
      BDOListToDelete.push_back(pSdoBdo);
    }
    m_SdoReadBoundList.removeAt(i);
  }
  
  for (i = m_SdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWSlaveDataObject *pSdo = m_SdoWriteBoundList[i];
    pSdo->unbindSdo(this);
    if (GTWBaseDataObject *pSdoBdo = pSdo->getBdo())
    {
      if (pSdoBdo->GetParentCollection())
      {
        pSdoBdo->GetParentCollection()->RemoveCollectionMember(pSdoBdo);
      }
      if (std::find(BDOListToDelete.begin(), BDOListToDelete.end(), pSdoBdo) == BDOListToDelete.end())
      {
        BDOListToDelete.push_back(pSdoBdo);
      }
    }
    m_SdoWriteBoundList.removeAt(i);
  }

  for (i = m_MdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWMasterDataObject *pMdo = m_MdoWriteBoundList[i];
    pMdo->unbindMdo(this);
    m_MdoWriteBoundList.removeAt(i);
  }

  // Now delete memory associated with removed SDOs
  for (auto it = BDOListToDelete.begin(); it != BDOListToDelete.end(); ++it)
  {
    delete *it;
  }
}

void GTWMasterDataObject::unbindSdos(bool bDeleteSdos)
{
  int i;

  for (i = m_SdoReadBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWSlaveDataObject *pSdo = m_SdoReadBoundList[i];
    pSdo->unbindSdo(this);
    m_SdoReadBoundList.removeAt(i);
    if (bDeleteSdos)
    {
      // FVE we may want to delete pSdo;
      pSdo->getBdo()->DeleteCollectionMember();
    }
  }

  for (i = m_SdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWSlaveDataObject *pSdo = m_SdoWriteBoundList[i];
    pSdo->unbindSdo(this);
    m_SdoWriteBoundList.removeAt(i);
    if (bDeleteSdos)
    {
      // FVE we may want to delete pSdo;
      pSdo->getBdo()->DeleteCollectionMember();
    }
  }
}

void GTWMasterDataObject::unbindMdo(GTWMasterDataObject *pMdo)
{
  int i;
  for (i = m_MdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    if (m_MdoWriteBoundList[i] == pMdo) 
    {
      m_MdoWriteBoundList.removeAt(i);
    }
  }

  for (i = m_MdoReadBoundList.getSize() - 1; i >= 0; i--)
  {
    if (m_MdoReadBoundList[i] == pMdo)
    {
      m_MdoReadBoundList.removeAt(i);
      if (m_pVariantReadCnvtr && m_pVariantReadCnvtr->getMDO() == pMdo)
      {
        delete m_pVariantReadCnvtr;
        m_pVariantReadCnvtr = nullptr;
      }
      break;
    }
  }
}
  
/**********************************************************************************\
	Function :			GTWMasterDataObject::unbindMdo
	Description :[none]	
	Return :			void	-	
	Parameters :
			GTWSlaveDataObject *pSdo	-	
	Note :[none]
\**********************************************************************************/
void GTWMasterDataObject::unbindMdo(GTWSlaveDataObject *pSdo)
{
  int i;
  
  for (i = m_SdoReadBoundList.getSize() - 1; i >= 0; i--)
  {
    if (m_SdoReadBoundList[i] == pSdo) 
    {
      pSdo->unbindSdo(this);
      m_SdoReadBoundList.removeAt(i);
    }
  }
  
  for (i = m_SdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    if (m_SdoWriteBoundList[i] == pSdo) 
    {
      pSdo->unbindSdo(this);
      m_SdoWriteBoundList.removeAt(i);
    }
  }
}

GTWMasterDataObject *GTWMasterDataObject::loopThroughBoundMdos(bool *bFoundBoundMdo, bool(*stopLoopingFunction)(GTWMasterDataObject *pMdo, GTW_MAPPING_DIR dir, void *pParam), void *pParam)
{
  *bFoundBoundMdo = TMWDEFS_FALSE;
  int i;
  
  //for (i = m_MdoReadBoundList.getSize() - 1; i >= 0; i--)
  //{
  //  GTWMasterDataObject *pMdo  = m_MdoReadBoundList[i];
  //  *bFoundBoundMdo = TMWDEFS_TRUE;
  //  
  //  if (stopLoopingFunction != TMWDEFS_NULL)
  //  {
  //    if (stopLoopingFunction(pMdo, pParam)) 
  //    {
  //      return (pMdo);
  //    }
  //  }
  //}

  for (i = m_MdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWMasterDataObject *pMdo  = m_MdoWriteBoundList[i];
    if (pMdo->SupportsNewPersistence()) // WHY are we checking this??
    {
      continue;
    }
    *bFoundBoundMdo = TMWDEFS_TRUE;
    
    if (stopLoopingFunction != TMWDEFS_NULL)
    {
      if (stopLoopingFunction(pMdo, GTW_MAPPING_DIR::GTW_MAPPING_DIR_RIGHT, pParam))
      {
        return (pMdo);
      }
    }
  }
  
  for (i = m_MdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWMasterDataObject *pMdo  = m_MdoWriteBoundList[i];
    *bFoundBoundMdo = TMWDEFS_TRUE;
    
    // check to see if it is in the read bound list
    // if so don't return it again
    for (int readIndex = m_MdoWriteBoundList.getSize() - 1; readIndex >= 0; readIndex--)
    {
      GTWMasterDataObject const *pWriteSdo  = m_MdoWriteBoundList[readIndex];
      if (pMdo == pWriteSdo)
      {

        return (TMWDEFS_NULL);
      }
    }

    if (stopLoopingFunction != TMWDEFS_NULL)
    {
      if (stopLoopingFunction(pMdo, GTW_MAPPING_DIR::GTW_MAPPING_DIR_RIGHT, pParam))
        return (pMdo);
    }
  }
  
  return (TMWDEFS_NULL);
}

GTWMasterDataObject *GTWMasterDataObject::loopThroughBoundMdosListView(bool *bFoundBoundMdo, bool(*stopLoopingFunction)(GTWMasterDataObject *pMdo, GTW_MAPPING_DIR dir, void *pParam), void *pParam)
{
  *bFoundBoundMdo = TMWDEFS_FALSE;
  int i;

  for (i = m_MdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWMasterDataObject *pMdo = m_MdoWriteBoundList[i];
    *bFoundBoundMdo = TMWDEFS_TRUE;

    if (stopLoopingFunction != TMWDEFS_NULL)
    {
      if (stopLoopingFunction(pMdo, GTW_MAPPING_DIR::GTW_MAPPING_DIR_RIGHT, pParam))
      {
        return (pMdo);
      }
    }
  }

  for (i = m_MdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWMasterDataObject *pMdo = m_MdoWriteBoundList[i];
    *bFoundBoundMdo = TMWDEFS_TRUE;

    // check to see if it is in the read bound list
    // if so just continue was handled above
    bool bFound = false;
    for (int readIndex = m_MdoWriteBoundList.getSize() - 1; readIndex >= 0; readIndex--)
    {
      GTWMasterDataObject const *pWriteSdo = m_MdoWriteBoundList[readIndex];
      if (pMdo == pWriteSdo)
      {
        bFound = true;
        break;
       //return (TMWDEFS_NULL);
      }
    }
    if (bFound)
    {
      continue;
    }

    if (stopLoopingFunction != TMWDEFS_NULL)
    {
      if (stopLoopingFunction(pMdo, GTW_MAPPING_DIR::GTW_MAPPING_DIR_RIGHT, pParam))
        return (pMdo);
    }
  }

  return (TMWDEFS_NULL);
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::loopThroughBoundSdos
	Description :[none]	
	Return :			GTWSlaveDataObject *	-	
	Parameters :
			bool *bFoundBoundSdo	-	
			bool(*stopLoopingFunction	-	
	Note :[none]
\**********************************************************************************/
GTWSlaveDataObject *GTWMasterDataObject::loopThroughBoundSdos(bool *bFoundBoundSdo, bool(*stopLoopingFunction)(GTWSlaveDataObject *pSdo, GTW_MAPPING_DIR dir, void *pParam), void *pParam, bool checkDeletable)
{
  *bFoundBoundSdo = TMWDEFS_FALSE;
  int i;
  
  for (i = m_SdoReadBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWSlaveDataObject *pSdo  = m_SdoReadBoundList[i];
    if ((!checkDeletable) || (pSdo->canBeDeleted() == false))
    {
      *bFoundBoundSdo = TMWDEFS_TRUE;
    }
    
    if (stopLoopingFunction != TMWDEFS_NULL)
    {
      if (stopLoopingFunction(pSdo, GTW_MAPPING_DIR::GTW_MAPPING_DIR_RIGHT, pParam))
      {
        return (pSdo);
      }
    }
  }
  
  for (i = m_SdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWSlaveDataObject *pSdo  = m_SdoWriteBoundList[i];
    if ((!checkDeletable) || (pSdo->canBeDeleted() == false))
    {
      *bFoundBoundSdo = TMWDEFS_TRUE;
    }
    
    // check to see if it is in the read bound list
    // if so don't return it again
    bool bFound = false;
    for (int readIndex = m_SdoReadBoundList.getSize() - 1; readIndex >= 0; readIndex--)
    {
      GTWSlaveDataObject const *pWriteSdo  = m_SdoReadBoundList[readIndex];
      if (pSdo == pWriteSdo)
      {
        bFound = true;
        break;
        //return (TMWDEFS_NULL);
      }
    }
    if (bFound)
    {
      continue;
    }

    if (stopLoopingFunction != TMWDEFS_NULL)
    {
      if (stopLoopingFunction(pSdo, GTW_MAPPING_DIR::GTW_MAPPING_DIR_LEFT, pParam))
        return (pSdo);
    }
  }
  
  return (TMWDEFS_NULL);
}

GTWSlaveDataObject *GTWMasterDataObject::loopThroughBoundCommandSdos(bool *bFoundBoundSdo, bool(*stopLoopingFunction)(GTWSlaveDataObject *pSdo, GTW_MAPPING_DIR dir, void *pParam), void *pParam)
{
  *bFoundBoundSdo = TMWDEFS_FALSE;

  for (int i = m_SdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWSlaveDataObject *pSdo  = m_SdoWriteBoundList[i];
#if USE_OPC_44
    if (dynamic_cast<GTWOpcSlaveDataObject*>(pSdo))
      continue;
 
#endif // USE_OPC_44

#if USE_OPC_UA
    if (dynamic_cast<GTWOpcUaSlaveDataObject*>(pSdo))
      continue;
#endif

    if (pSdo->isCommand())
    {
      *bFoundBoundSdo = TMWDEFS_TRUE;
    }

    // check to see if it is in the read bound list
    // if so don't return it again
    for (int readIndex = m_SdoReadBoundList.getSize() - 1; readIndex >= 0; readIndex--)
    {
      GTWSlaveDataObject const *pWriteSdo  = m_SdoReadBoundList[readIndex];
      if (pSdo == pWriteSdo)
      {
        return (TMWDEFS_NULL);
      }
    }

    if (stopLoopingFunction != TMWDEFS_NULL)
    {
      if (stopLoopingFunction(pSdo, GTW_MAPPING_DIR::GTW_MAPPING_DIR_RIGHT, pParam))
        return (pSdo);
    }
  }

  return (TMWDEFS_NULL);
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::getOPCQuality
	Description :[none]	
	Return :			unsigned short	-	
	Parameters :
			GTWDEFS_UPDTRSN updateReason	-	
	Note :[none]
\**********************************************************************************/
unsigned short GTWMasterDataObject::getOPCQuality(GTWDEFS_UPDTRSN updateReason)
{
#if USE_OPC_44
  GTWDEFS_STD_QLTY stdQuality = getMdoStdQuality();
  unsigned short           opcQuality = EnumQuality_BAD;
  
  if (stdQuality & GTWDEFS_STD_QLTY_UNINITIALIZED)
  {
    opcQuality = EnumQuality_BAD_NOT_CONNECTED;
  }
  else if (stdQuality & GTWDEFS_STD_QLTY_BLOCKED)
  {
    opcQuality = EnumQuality_BAD_OUT_OF_SERVICE;
  }
  else if (stdQuality & GTWDEFS_STD_QLTY_INVALID)
  {
    opcQuality = EnumQuality_BAD;
  }
  else if (stdQuality & GTWDEFS_STD_QLTY_REF_ERROR)
  {
    opcQuality = EnumQuality_UNCERTAIN_SENSOR_CAL;
  }
  else if (stdQuality & GTWDEFS_STD_QLTY_OVERFLOW)
  {
    opcQuality = EnumQuality_UNCERTAIN_EGU_EXCEEDED;
  }
  else if (stdQuality & GTWDEFS_STD_QLTY_NOT_TOPICAL)
  {
    opcQuality = EnumQuality_UNCERTAIN_LAST_USABLE;
  }
  else if (stdQuality & GTWDEFS_STD_QLTY_IN_TRANSIT)
  {
    opcQuality = EnumQuality_UNCERTAIN;
  }
  else if (stdQuality & GTWDEFS_STD_QLTY_SUBSTITUTED)
  {
    opcQuality = EnumQuality_GOOD_LOCAL_OVERRIDE;
  }
  else 
  {
    opcQuality = EnumQuality_GOOD;
    if (updateReason & GTWDEFS_UPDTRSN_CTRL_BY_COMM || updateReason & GTWDEFS_UPDTRSN_CTRL_AT_DEVICE)
    {
      opcQuality = EnumQuality_GOOD_LOCAL_OVERRIDE;
    }
  }
  return opcQuality;
#else
  return 0;
#endif
}

#if USE_OPC_44
tstring ToHex(const unsigned long& s)
{
  ostringstream ret;

  ret << std::hex << s;
  //bool upper_case = true;
  //for (string::size_type i = 0; i < s.length(); ++i)
  //  ret << std::hex << std::setfill('0') << std::setw(2) << (upper_case ? std::uppercase : std::nouppercase) << (int)s[i];

  return ret.str();
}

void GTWMasterDataObject::fireChanges(GTWDEFS_UPDTRSN updateReason, CStdString msg)
{
  //m_activeOrNotAcknowledgedCond->change();


  OPCAEServerDebugMsg(updateReason, msg);

  getApp()->changeCondition(m_activeOrNotAcknowledgedCond);
}

void GTWMasterDataObject::OPCAEServerDebugMsg(GTWDEFS_UPDTRSN updateReason, CStdString fireMsg)
{
  CStdString reasonStr;
  reasonStr.Format("0X%04X", updateReason);

  char ackTimeStr[256];
  DateTime ack_time = m_activeOrNotAcknowledgedCond->getAckTime();
  FILETIME file_ack_time = ack_time.get();
  TMWDTIME tmw_ack_time;
  bool bSuccess = GtwOsDateTime::ConvertFileTimeToTMWDTIME(&file_ack_time, &tmw_ack_time, true);
  if (bSuccess)
  {
    tmw_ack_time.genuineTime = TMWDEFS_TRUE;
    tmw_ack_time.qualifier = TMWDTIME_ABSOLUTE;
  }
  tmwdiag_time2string(&tmw_ack_time, TMWDEFS_TIME_FORMAT_56, ackTimeStr, 256, false);

  char occurenceTimeStr[256];
  DateTime occurence_time = m_activeOrNotAcknowledgedCond->getOccurenceTime();
  FILETIME file_occurence_time = occurence_time.get();
  TMWDTIME tmw_occurence_time;
  bSuccess = GtwOsDateTime::ConvertFileTimeToTMWDTIME(&file_occurence_time, &tmw_occurence_time, true);
  if (bSuccess)
  {
    tmw_occurence_time.genuineTime = TMWDEFS_TRUE;
    tmw_occurence_time.qualifier = TMWDTIME_ABSOLUTE;
  }
  tmwdiag_time2string(&tmw_occurence_time, TMWDEFS_TIME_FORMAT_56, occurenceTimeStr, 256, false);


  GtwVariant mdoValue;
  CStdString mdoValueAsString;
  CStdString attrValueAsString;

  this->getMdoValueAsVariant(mdoValue);
  mdoValue.GetValueAsString(mdoValueAsString);


  if (std::vector<Variant> curEventAttrs = m_activeOrNotAcknowledgedCond->getEventAttributes(); !curEventAttrs.empty())
  {
    Variant curAttrV = curEventAttrs[0];
    GtwVariant curAttrVal;
    curAttrVal.AssignFromVariant(curAttrV);
    curAttrVal.GetValueAsString(attrValueAsString);

  }

  CStdString msg = "";
  msg = msg + " fireMsg:" + fireMsg;
  msg = msg + " reason:" + reasonStr;
  msg = msg + " ackComment:" + CStdString(m_activeOrNotAcknowledgedCond->getAckComment().size() == 0 ? CStdString("<null>") : m_activeOrNotAcknowledgedCond->getAckComment());
  msg = msg + " ackId:" + CStdString(m_activeOrNotAcknowledgedCond->getAckId().size() == 0 ? CStdString("<null>") : m_activeOrNotAcknowledgedCond->getAckId());
  msg = msg + " ackRequired:" + CStdString(m_activeOrNotAcknowledgedCond->getAckRequired() == FALSE ? CStdString("false") : CStdString("true"));
  msg = msg + " ackTime:" + CStdString(ackTimeStr);
  msg = msg + " activeSubConditionName:" + CStdString(m_activeOrNotAcknowledgedCond->getActiveSubConditionName().size() == 0 ? CStdString("<null>") : m_activeOrNotAcknowledgedCond->getActiveSubConditionName());
  msg = msg + " Category:" + CStdString(m_activeOrNotAcknowledgedCond->getCategory()->getName().size() == 0 ? CStdString("<null>") : m_activeOrNotAcknowledgedCond->getCategory()->getName());
  msg = msg + " ChangeMask:" + CStdString(ToHex((m_activeOrNotAcknowledgedCond->getChangeMask())));
  msg = msg + " AttributeValue:" + CStdString(attrValueAsString);
  msg = msg + " Message:" + CStdString(m_activeOrNotAcknowledgedCond->getMessage().size() == 0 ? CStdString("<null>") : m_activeOrNotAcknowledgedCond->getMessage());
  msg = msg + " Name:" + CStdString(m_activeOrNotAcknowledgedCond->getName().size() == 0 ? CStdString("<null>") : m_activeOrNotAcknowledgedCond->getName());
  msg = msg + " OccurenceTime:" + CStdString(occurenceTimeStr);
  msg = msg + " Quality:" + CStdString(std::to_string(m_activeOrNotAcknowledgedCond->getQuality()));
  msg = msg + " Severity:" + CStdString(std::to_string(m_activeOrNotAcknowledgedCond->getSeverity()));
  msg = msg + " SourcePath:" + CStdString(m_activeOrNotAcknowledgedCond->getSourcePath().size() == 0 ? CStdString("<null>") : m_activeOrNotAcknowledgedCond->getSourcePath());
  msg = msg + " StateChange:" + CStdString(ToHex((m_activeOrNotAcknowledgedCond->getStateChange())));
  msg = msg + " UserData:" + CStdString(std::to_string(m_activeOrNotAcknowledgedCond->getUserData()));

  CStdString sMsg;
  sMsg.Format("OPCAE-Server-debug Fire Condition Event Change: %s - %s", this->getBdo()->GetFullName().c_str(), msg.c_str());
  //TMWDIAG_MESSAGE(sMsg.c_str(), TMWDIAG_ID_OPC);
  LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_OPC_DEEP, nullptr, "%s", sMsg.c_str());
}

bool GTWMasterDataObject::validateConditionName( CStdString n )
{
  bool isSubCond = false;
  bool isCond = false;

  CStdString lookUpCondition = n;
  CStdString subCondName = "";
  CStdString condName = "";
  if (lookUpCondition != "")
  {
    char *buf = (char *)calloc(lookUpCondition.GetLength()+1,1);
    util::strcpy(buf, lookUpCondition.GetLength()+1, lookUpCondition.GetBuffer());
    int fldCnt = 1;
    for (size_t i = 0 ;i<util::strlen(buf); i++)
    {
      if (buf[i] == ':')
      {
        ++fldCnt;
        buf[i] = 0x7f;
      }
      if (fldCnt == 2 && buf[i] != 0x7f)
      {
        condName += CStdString(std::string(1, buf[i]));
      }
      if (fldCnt == 3 && buf[i] != 0x7f)
      {
        subCondName += CStdString(std::string(1, buf[i]));
      }
    }
    if (fldCnt > 1)
    {
      std::vector<AeCategory*> catList = getApp()->getAeCategoryList();
      for (auto & element : catList)
      {
        if (fldCnt == 2)
        {
          if (element->getConditionDefinition(condName) != NULL)
          {
            isCond = true;
          }
        }
        if (fldCnt == 3)
        {
          if (element->getSubConditionDefinition(condName, subCondName) != NULL)
          {
            isSubCond = true;
          }
        }
      }
    }
    free(buf);
  }

  if (isSubCond == true || isCond == true)
  {
    return true;
  }
  return false;
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::reportOPCevent
	Description :[none]	
	Return :			void	-	
	Parameters :
			GTWDEFS_UPDTRSN	-	
	Note :[none]
\**********************************************************************************/
void GTWMasterDataObject::reportOPCConditionEvent(GTWDEFS_UPDTRSN updateReason, bool bOnlyActive)
{
  tmw::CriticalSectionLock lock(m_OPCConditionEventCS);
  if (GetGTWApp()->IsShuttingDown())
  {
    return;
  }

  if (getOpcServer() == NULL || GetGTWApp()->getOPCAEServerLicensed() == TMWDEFS_FALSE)  // no OPC event manger available or not licensed
  {
    return;
  }

  if (GetGTWApp()->IsOpcReady == false)
  {
    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_OPC_DEEP, nullptr, "%s", "IsOpcReady FALSE");
    tmw::CriticalSectionLock lock(GTWmain::g_OpcAeInitialMdosCriticalSection);

    std::list<GTWMasterDataObject*>::iterator it;
    it = std::find(GetGTWApp()->OpcAeInitialMdos.begin(), GetGTWApp()->OpcAeInitialMdos.end(), this);
    if (it == GetGTWApp()->OpcAeInitialMdos.end())
    {
      this->m_upateReason = updateReason;
      GetGTWApp()->OpcAeInitialMdos.push_back(this);
    }
    return;
  }

  LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_OPC_DEEP, nullptr, "%s", "IsOpcReady TRUE");
  if (bOnlyActive)
  {
    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_OPC_DEEP, nullptr, "%s", "reportOPCConditionEvent ONLY ACTIVE TRUE");
  }
  else
  {
    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_OPC_DEEP, nullptr, "%s", "reportOPCConditionEvent ONLY ACTIVE FALSE");
  }

  GTWBaseDataObject* pBdo = getBdo();
  if (pBdo == NULL)
  {
    return;
  }

  if (m_activeOrNotAcknowledgedCond == nullptr)
  {
    SetupActiveConditionNames();
    SetupInActiveConditionNames();

    if (m_pCategoryActive == NULL)
    {
      CStdString errStr;
      errStr.Format("Can not report OPC AE condition event for %s (active/inactive condition names are not setup)", this->getBdo()->GetFullName());
      TMWDIAG_ERROR(errStr);
      return;
    }
    m_activeOrNotAcknowledgedCond = new AeCondition();
  }

  FILETIME       timestamp;
  timestamp.dwLowDateTime = timestamp.dwHighDateTime = 0;

  TMWDTIME timeToReport;
  GTWDEFS_TIME_QLTY timeToReportQuality;

  CoFileTimeNow(&timestamp);
  if (getOPCAETimeSource() == GTWDEFS_UPDATE_TIME)
  {
    pBdo->getMdoUpdatedTime(&timeToReport, &timeToReportQuality, GetGTWApp()->gtwOpcTimeZone);
  }
  else if (getOPCAETimeSource() == GTWDEFS_REPORTED_TIME)
  {
    pBdo->getMdoReportedTimeEx(&timeToReport, &timeToReportQuality, GetGTWApp()->gtwOpcTimeZone);
  }

  if ((getOPCAETimeSource() == GTWDEFS_UPDATE_TIME || getOPCAETimeSource() == GTWDEFS_REPORTED_TIME) && timeToReport.invalid == TMWDEFS_FALSE)
  {
    SYSTEMTIME     systemTime;
    FILETIME localFileTime;
    // convert timestamp to system time then to filetime
    systemTime.wYear = timeToReport.year;
    systemTime.wMonth = timeToReport.month;
    systemTime.wDayOfWeek = timeToReport.dayOfWeek % 7;
    systemTime.wDay = timeToReport.dayOfMonth;
    systemTime.wHour = timeToReport.hour;
    systemTime.wMinute = timeToReport.minutes;
    systemTime.wSecond = timeToReport.mSecsAndSecs / 1000;
    systemTime.wMilliseconds = timeToReport.mSecsAndSecs % 1000;
    SystemTimeToFileTime(&systemTime, &localFileTime);
    LocalFileTimeToFileTime(&localFileTime, &timestamp);
  }

  CStdString srcName = getBdo()->GetOPCFullName();
  m_activeOrNotAcknowledgedCond->setSourcePath(srcName);
  //if (m_sAEAlarmValue == "")
  //{
  m_activeOrNotAcknowledgedCond->setCategory(m_pCategoryActive);
  m_activeOrNotAcknowledgedCond->setDefinition(m_pCategoryActive->getConditionDefinition(m_condName));
  if (m_bIsSubCond)
  {
    m_activeOrNotAcknowledgedCond->setActiveSubConditionName(m_subCondName);
  }
  else
  {
    m_activeOrNotAcknowledgedCond->setActiveSubConditionName(m_condName);
  }

  unsigned short quality = getOPCQuality(updateReason);

  m_activeOrNotAcknowledgedCond->setQuality((EnumQuality)quality);

  GtwVariant cvAttrValue;
  CStdString cvAttrValueStr;

  this->getMdoValueAsVariant(cvAttrValue);
  cvAttrValue.GetValueAsString(cvAttrValueStr);

  std::vector<Variant> curEventAttrs = m_activeOrNotAcknowledgedCond->getEventAttributes();

  bool attrChanged = false;
  if (!curEventAttrs.empty())
  {
    Variant curAttrV = curEventAttrs[0];
    GtwVariant curAttrVal;
    curAttrVal.AssignFromVariant(curAttrV);

    if (cvAttrValue.GetBooleanValue() != curAttrVal.GetBooleanValue())
    {
      attrChanged = true;
    }
  }

  if (cvAttrValue.GetBooleanValue() == true && m_sAEAlarmValue == "TRUE" && curEventAttrs.empty())
  {
    attrChanged = true;
  }
  if (cvAttrValue.GetBooleanValue() == false && m_sAEAlarmValue == "FALSE" && curEventAttrs.empty())
  {
    attrChanged = true;
  }

  CStdString description;
  description = getMdoDescription();

  if (description != "")
  {
    description = description + " - ";
  }

  m_activeOrNotAcknowledgedCond->setSeverity(m_OPCAESeverity);

  if (m_bOPCAEUseValueAsMessage == true)
  {
    GTWEXPND_FORMAT   expndFormat;
    expndFormat.setFormatPrefix("%");
    CStdString tMsg;
    getMdoValueAsString(expndFormat, tMsg);
    std::string temp(description.c_str());
    temp.append(" ");
    temp.append(m_sAEMessage);
    temp.append(" ");
    temp.append(tMsg.c_str());
    m_activeOrNotAcknowledgedCond->setMessage(temp);
  }
  else
  {

    std::string temp1(description.c_str());
    temp1.append(" ");
    temp1.append(m_sAEMessage);

    m_activeOrNotAcknowledgedCond->setMessage(temp1);
  }

  //m_activeOrNotAcknowledgedCond->setStateChange(EnumConditionState_ENABLED);
  if (m_sAEAlarmValue == "TRUE" && cvAttrValue.GetType() == GTWDEFS_TYPE_BOOL && cvAttrValue.GetBooleanValue() == true)
  {
    FireActiveCondition(cvAttrValue, timestamp, attrChanged, updateReason);
  }
  else if (m_sAEAlarmValue == "FALSE" && cvAttrValue.GetType() == GTWDEFS_TYPE_BOOL && cvAttrValue.GetBooleanValue() == false)
  {
    FireActiveCondition(cvAttrValue, timestamp, attrChanged, updateReason);
  }
  else if (m_sAEAlarmValue == "TRUE" && cvAttrValue.GetType() == GTWDEFS_TYPE_BOOL && cvAttrValue.GetBooleanValue() == false)
  {
    if (!bOnlyActive)
      FireInactiveCondition(cvAttrValue, timestamp, attrChanged, updateReason);
  }
  else if (m_sAEAlarmValue == "FALSE" && cvAttrValue.GetType() == GTWDEFS_TYPE_BOOL && cvAttrValue.GetBooleanValue() == true)
  {
    if (!bOnlyActive)
      FireInactiveCondition(cvAttrValue, timestamp, attrChanged, updateReason);
  }
  else if (m_sAEAlarmValue == cvAttrValueStr)
  {
    FireActiveCondition(cvAttrValue, timestamp, attrChanged, updateReason);
  }
  else if (m_sAEAlarmValue == "")
  {
    if (cvAttrValue.GetBooleanValue() == true)
    {
      FireActiveCondition(cvAttrValue, timestamp, attrChanged, updateReason);
    }
    else
    {
      if (!bOnlyActive)
        FireInactiveCondition(cvAttrValue, timestamp, attrChanged, updateReason);
    }
  }
  //else
  //{
  //  FireInactiveCondition(cvAttrValue, timestamp, attrChanged);
  //}

}

void GTWMasterDataObject::FireActiveCondition(GtwVariant cvAttrValue, FILETIME timestamp, bool attrChanged, GTWDEFS_UPDTRSN updateReason)
{
  if (m_bOPCAEFireCondOnAttrChange == true && attrChanged == false)
  {
    return;
  }

  tstring ack_id = m_activeOrNotAcknowledgedCond->getAckId();
  tstring ack_comment = m_activeOrNotAcknowledgedCond->getAckComment();
  DateTime ack_time = m_activeOrNotAcknowledgedCond->getAckTime();
  FILETIME file_ack_time = ack_time.get();
  TMWDTIME tmw_ack_time;
  if (bool bSuccess = GtwOsDateTime::ConvertFileTimeToTMWDTIME(&file_ack_time, &tmw_ack_time, true))
  {
    tmw_ack_time.genuineTime = TMWDEFS_TRUE;
    tmw_ack_time.qualifier = TMWDTIME_ABSOLUTE;
  }
  bool hasAckData = true;
  if (ack_id == "" && ack_comment == "" && tmw_ack_time.year == 1601)
  {
    hasAckData = false;
  }
  m_activeOrNotAcknowledgedCond->setChangeMask(EnumConditionChange(m_iOPCAE_ACTMASK));
  m_activeOrNotAcknowledgedCond->setName(m_condName);
  m_activeOrNotAcknowledgedCond->setCategory(m_pCategoryActive);
  m_activeOrNotAcknowledgedCond->setDefinition(m_pCategoryActive->getConditionDefinition(m_condName));

  m_activeOrNotAcknowledgedCond->setAckRequired(m_bOPCAEActiveAckRequired);

  if (m_bIsSubCond)
  {
    m_activeOrNotAcknowledgedCond->setActiveSubConditionName(m_subCondName);
  }
  else
  {
    m_activeOrNotAcknowledgedCond->setActiveSubConditionName(m_condName);
  }
  m_activeOrNotAcknowledgedCond->setSeverity(m_OPCAESeverity);

  std::vector<Variant> attributeValues;
  Variant attrValue;
  cvAttrValue.AssignToVariant(attrValue);
  attributeValues.push_back(attrValue);
  m_activeOrNotAcknowledgedCond->setEventAttributes(attributeValues);

  m_activeOrNotAcknowledgedCond->setOccurenceTime(&timestamp);
  m_activeOrNotAcknowledgedCond->setStateChange(EnumConditionState(m_iOPCAE_ACTSTATE));

  //m_activeOrNotAcknowledgedCond->change();
  fireChanges(updateReason, "FireActiveCondition");
}

void GTWMasterDataObject::FireInactiveCondition(GtwVariant cvAttrValue, FILETIME timestamp, bool attrChanged, GTWDEFS_UPDTRSN updateReason)
{
  if (m_bOPCAEFireCondOnAttrChange == true && attrChanged == false)
  {
    return;
  }

  tstring ack_id = m_activeOrNotAcknowledgedCond->getAckId();
  tstring ack_comment = m_activeOrNotAcknowledgedCond->getAckComment();
  DateTime ack_time = m_activeOrNotAcknowledgedCond->getAckTime();
  FILETIME file_ack_time = ack_time.get();
  TMWDTIME tmw_ack_time;
  if (bool bSuccess = GtwOsDateTime::ConvertFileTimeToTMWDTIME(&file_ack_time, &tmw_ack_time, true))
  {
    tmw_ack_time.genuineTime = TMWDEFS_TRUE;
    tmw_ack_time.qualifier = TMWDTIME_ABSOLUTE;
  }
  bool hasAckData = true;
  if (ack_id == "" && ack_comment == "" && tmw_ack_time.year == 1601)
  {
    hasAckData = false;
  }

  m_activeOrNotAcknowledgedCond->setChangeMask(hasAckData ? EnumConditionChange(m_iOPCAE_INACTMASK | EnumConditionChange_ACK_STATE) : EnumConditionChange(m_iOPCAE_INACTMASK));
  m_activeOrNotAcknowledgedCond->setName(m_condInActiveName);
  m_activeOrNotAcknowledgedCond->setCategory(m_pCategoryInActive);
  m_activeOrNotAcknowledgedCond->setDefinition(m_pCategoryInActive->getConditionDefinition(m_condInActiveName));

  m_activeOrNotAcknowledgedCond->setAckRequired(hasAckData ? false : m_bOPCAEInActiveAckRequired);
  if (m_bIsInActiveSubCond)
  {
    m_activeOrNotAcknowledgedCond->setActiveSubConditionName(m_subCondInActiveName);
  }
  else
  {
    m_activeOrNotAcknowledgedCond->setActiveSubConditionName(m_condInActiveName);
  }
  m_activeOrNotAcknowledgedCond->setSeverity(m_OPCAEInActiveSeverity);

  std::vector<Variant> attributeValues;
  Variant attrValue;
  cvAttrValue.AssignToVariant(attrValue);
  attributeValues.push_back(attrValue);
  m_activeOrNotAcknowledgedCond->setEventAttributes(attributeValues);

  m_activeOrNotAcknowledgedCond->setOccurenceTime(&timestamp);
  m_activeOrNotAcknowledgedCond->setStateChange(hasAckData ? EnumConditionState(EnumConditionState_ACKED | EnumConditionState_ENABLED) : EnumConditionState(m_iOPCAE_INACTSTATE));

  //m_activeOrNotAcknowledgedCond->change();
  fireChanges(updateReason, "FireInactiveCondition");

  if (hasAckData)
  {
    char timeStr[256];

    tmwdiag_time2string(&tmw_ack_time, TMWDEFS_TIME_FORMAT_56, timeStr, 256, false);

    CStdString sMsg;
    sMsg.Format("OPCAE-Server-ack Condition Event for %s was acknowledged by: %s (%s) at %s", this->getBdo()->GetFullName().c_str(), ack_id, ack_comment, timeStr);
    //TMWDIAG_MESSAGE(sMsg.c_str(), TMWDIAG_ID_OPC);
    LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_OPC, nullptr, "%s",sMsg.c_str());

    tstring clearString = "";
    DateTime clearDate;
    clearDate.clear();
    m_activeOrNotAcknowledgedCond->setAckComment(clearString);
    m_activeOrNotAcknowledgedCond->setAckId(clearString);
    m_activeOrNotAcknowledgedCond->setAckTime(clearDate);
  }
}

void GTWMasterDataObject::SetupInActiveConditionNames()
{
  // find cat/condition/sub condition name
  // has to be in format as cat:cond:subcond or cat:cond
  CStdString lookUpConditionInActive = m_sAEInActiveConditionName;
  m_subCondInActiveName = "";
  m_condInActiveName = "";
  if (lookUpConditionInActive != "")
  {
    char *buf = (char *)calloc(lookUpConditionInActive.GetLength()+1,1);
    util::strcpy(buf, lookUpConditionInActive.GetLength()+1, lookUpConditionInActive.GetBuffer());
    int fldCnt = 1;
    for (size_t i = 0 ;i<util::strlen(buf); i++)
    {
      if (buf[i] == ':')
      {
        ++fldCnt;
        buf[i] = 0x7f;
      }
      if (fldCnt == 2 && buf[i] != 0x7f)
      {
        m_condInActiveName += CStdString(std::string(1, buf[i]));
      }
      if (fldCnt == 3 && buf[i] != 0x7f)
      {
        m_subCondInActiveName += CStdString(std::string(1, buf[i]));
      }
    }
    if (fldCnt > 1)
    {
      std::vector<AeCategory*> catList = getApp()->getAeCategoryList();
      for (auto & element : catList)
      {
        if (fldCnt == 2)
        {
          if (element->getConditionDefinition(m_condName) != NULL)
          {
            m_pCategoryInActive = element;
            break;
          }
        }
        if (fldCnt == 3)
        {
          if (element->getSubConditionDefinition(m_condName, m_subCondName) != NULL)
          {
            m_pCategoryInActive = element;
            m_bIsInActiveSubCond = true;
            break;
          }
        }
      }
    }
    free(buf);
  }

  if (m_pCategoryInActive == NULL)
  {
    m_pCategoryInActive =  getOpcServer()->getCategory(tstring(_T(SDGAE_CONDITION_COS)));

    m_subCondInActiveName = "";
    m_condInActiveName = SDGAE_CONDITION_COS;
  }
}

void GTWMasterDataObject::SetupActiveConditionNames()
{
  // find cat/condition/sub condition name
  // has to be in format as cat:cond:subcond or cat:cond
  CStdString lookUpCondition = m_sAEConditionName;
  m_subCondName = "";
  m_condName = "";
  if (lookUpCondition != "")
  {
    char *buf = (char *)calloc(lookUpCondition.GetLength()+1,1);
    util::strcpy(buf, lookUpCondition.GetLength()+1, lookUpCondition.GetBuffer());
    int fldCnt = 1;
    for (size_t i = 0 ;i<util::strlen(buf); i++)
    {
      if (buf[i] == ':')
      {
        ++fldCnt;
        buf[i] = 0x7f;
      }
      if (fldCnt == 2 && buf[i] != 0x7f)
      {
        m_condName += CStdString(std::string(1, buf[i]));
      }
      if (fldCnt == 3 && buf[i] != 0x7f)
      {
        m_subCondName += CStdString(std::string(1, buf[i]));
      }
    }

    if (fldCnt > 1)
    {
      std::vector<AeCategory*> catList = getApp()->getAeCategoryList();
      for (auto & element : catList)
      {
        if (fldCnt == 2)
        {
          if (element->getConditionDefinition(m_condName) != NULL)
          {
            m_pCategoryActive = element;
            break;
          }
        }
        if (fldCnt == 3)
        {
          if (element->getSubConditionDefinition(m_condName, m_subCondName) != NULL)
          {
            m_pCategoryActive = element;
            m_bIsSubCond = true;
            break;
          }
        }
      }
    }

    free(buf);
  }

  if (m_pCategoryActive == NULL)
  {
    m_pCategoryActive = getOpcServer()->getCategory(tstring(_T(SDGAE_CONDITION_COS)));

    m_subCondName = "";
    m_condName = SDGAE_CONDITION_COS;
  }
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::reportOPCSimpleEvent
	Description :[none]	
	Return :			void	-	
	Parameters :
			GTWDEFS_UPDTRSN updateReason	-	
	Note :[none]
\**********************************************************************************/
void GTWMasterDataObject::reportOPCSimpleEvent(GTWDEFS_UPDTRSN updateReason)
{
  if (GetGTWApp()->IsShuttingDown())
  {
    return;
  }

  if (getOpcServer() == NULL || GetGTWApp()->getOPCAEServerLicensed() == TMWDEFS_FALSE)  // no OPC event manger available or not licensed
  {
    return;
  }

  if (GetGTWApp()->IsOpcReady != true)
  {
    tmw::CriticalSectionLock lock(GTWmain::g_OpcAeInitialMdosCriticalSection);

    std::list<GTWMasterDataObject*>::iterator it;
    it = std::find(GetGTWApp()->OpcAeInitialMdos.begin(), GetGTWApp()->OpcAeInitialMdos.end(), this);
    if (it == GetGTWApp()->OpcAeInitialMdos.end())
    {
      this->m_upateReason = updateReason;
      GetGTWApp()->OpcAeInitialMdos.push_back(this);
    }
    return;
  }

  GTWBaseDataObject *pBdo = getBdo();
  if (pBdo == nullptr)
  {
    return;
  }

  unsigned int catID = 0;
  CStdString catMsg;

  if (OpcServer::getUpdateReasonID(updateReason, catID, catMsg))
  {
    CStdString description;
    description = getMdoDescription();

    if (description != "")
    {
      description = description + " - ";
    }

    if (m_sAEMessage == "" && m_bOPCAEUseValueAsMessage == false)
    {
      CStdString msg;
      CStdString value;
	    GTWEXPND_FORMAT   expndFormat;
	    expndFormat.setFormatPrefix("%");
	
	    getMdoValueAsString( expndFormat, value);
      msg.Format("%s %s changed to: %s", description,getBdo()->GetFullName(),value);
	
	    catMsg += msg;
    }
    else if (m_bOPCAEUseValueAsMessage == true)
    {
      GTWEXPND_FORMAT   expndFormat;
      expndFormat.setFormatPrefix("%");
      CStdString tMsg;
      getMdoValueAsString( expndFormat, tMsg);
      catMsg = description + tMsg;
    }
    else
    {
      catMsg = description + m_sAEMessage;
    }

    tstring fullName(getBdo()->GetFullName());
    AeEvent event((EnumEventType)GTW_EnumEventType_SIMPLE_EVT, fullName, catID);
    if (m_OPCAESeverity != 0)
    {
      event.setSeverity(m_OPCAESeverity);
    }
    event.setMessage(catMsg);

    TMWDTIME timeToReport;
    GTWDEFS_TIME_QLTY timeToReportQuality;
 
    if (getOPCAETimeSource() == GTWDEFS_UPDATE_TIME)
    {
      pBdo->getMdoUpdatedTime(&timeToReport, &timeToReportQuality, GetGTWApp()->gtwOpcTimeZone);
    }
    else if (getOPCAETimeSource() == GTWDEFS_REPORTED_TIME)
    {
      pBdo->getMdoReportedTime(&timeToReport, &timeToReportQuality, GetGTWApp()->gtwOpcTimeZone);
    }

    if ((getOPCAETimeSource() == GTWDEFS_UPDATE_TIME || getOPCAETimeSource() == GTWDEFS_REPORTED_TIME) && timeToReport.invalid == TMWDEFS_FALSE)
    {
      FILETIME      localFileTime;
      GtwOsDateTime reportDateTime(&timeToReport);
      reportDateTime.GetAsFileTime(&localFileTime);

//      GtwOsDateTime reportDateTime(&timeToReport, TMWDEFS_TRUE);
      //FILETIME      localFileTime;
      //reportDateTime.getFileTime(&localFileTime);
      DateTime occurTime(&localFileTime);
      event.setOccurenceTime(occurTime);
    }

    GtwVariant Value;
    this->getMdoValueAsVariant(Value);
    std::vector<Variant> attrList(0);
    Variant vVal;
    Value.AssignToVariant(vVal);
    attrList.push_back(vVal);
    event.setAttributeValueList(attrList);

    //event.fire();
    std::vector<AeEvent*> events;

    events.push_back(&event);
    getApp()->fireEvents(events);
  }
}

#endif  // USE_OPC_44

#if USE_OPC_UA
GTWOpcUaSlaveDataObject* GTWMasterDataObject::GetOpcUaSDO()
{
  GTWOpcUaSlaveDataObject* pUaSdo;
  for (int i = m_SdoReadBoundList.getSize() - 1; i >= 0; i--)
  {
    pUaSdo = dynamic_cast<GTWOpcUaSlaveDataObject*>(m_SdoReadBoundList[i]);
    if (pUaSdo)
    {
      return pUaSdo;
    }
  }

  for (int i = m_SdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    pUaSdo = dynamic_cast<GTWOpcUaSlaveDataObject*>(m_SdoWriteBoundList[i]);
    if (pUaSdo)
    {
      return pUaSdo;
    }
  }

  return nullptr;
}
#endif

/**********************************************************************************\
	Function :			GTWMasterDataObject::HasMappedSdo
	Description : [none]	
	Return :			bool	-	
	Parameters :
	Note : [none]
\**********************************************************************************/
bool GTWMasterDataObject::HasMappedSdo(bool checkDeletable)
{
  bool bFoundBoundAnySdo;
  loopThroughBoundSdos(&bFoundBoundAnySdo,TMWDEFS_NULL,TMWDEFS_NULL,checkDeletable);
  return bFoundBoundAnySdo;
}

bool GTWMasterDataObject::HasNonOpcMappedSdo()
{
  int i;
  for (i = m_SdoReadBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWSlaveDataObject *pSdo = m_SdoReadBoundList[i];
#if USE_OPC_44 && USE_OPC_UA
    if (!dynamic_cast<GTWOpcSlaveDataObject*>(pSdo) && !dynamic_cast<GTWOpcUaSlaveDataObject*>(pSdo))
    {
      return true;
    }
#endif
#if USE_OPC_UA
    if (!dynamic_cast<GTWOpcUaSlaveDataObject*>(pSdo))
    {
      return true;
    }
#endif
  }

  for (i = m_SdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWSlaveDataObject *pSdo = m_SdoWriteBoundList[i];
#if USE_OPC_44 && USE_OPC_UA
    if (!dynamic_cast<GTWOpcSlaveDataObject*>(pSdo) && !dynamic_cast<GTWOpcUaSlaveDataObject*>(pSdo))
    {
      return true;
    }
#endif
#if USE_OPC_UA
    if (!dynamic_cast<GTWOpcUaSlaveDataObject*>(pSdo))
    {
      return true;
    }
#endif
  }

  return false;
}

bool GTWMasterDataObject::HasMappedSdoAndCannotDelete()
{
  return HasMappedSdo(true);
}

bool GTWMasterDataObject::HasMappedCommandSdo()
{
  if (m_bAllowCommand)
  {
    return false;
  }
  bool bFoundBoundAnySdo;
  loopThroughBoundCommandSdos(&bFoundBoundAnySdo,TMWDEFS_NULL,TMWDEFS_NULL);
  return bFoundBoundAnySdo;
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::HasMappedSdo
	Description : [none]	
	Return :			bool	-	
	Parameters :
	Note : [none]
\**********************************************************************************/
bool GTWMasterDataObject::HasMappedMdo()
{
  bool bFoundBoundAnyMdo;
  loopThroughBoundMdos(&bFoundBoundAnyMdo,TMWDEFS_NULL,TMWDEFS_NULL);
  return bFoundBoundAnyMdo;
}

bool GTWMasterDataObject::IsMapped(CStdString& sMappedTo)
{
  uint32_t nCount = 0;
  // Check for SDO mappings
  int i;
  for (i = m_SdoReadBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWSlaveDataObject* pSdo = m_SdoReadBoundList[i];

#if USE_OPC_44
    if (dynamic_cast<GTWOpcSlaveDataObject*>(pSdo))
    {
      continue;
    }
#endif
#if USE_OPC_UA
    if (dynamic_cast<GTWOpcUaSlaveDataObject*>(pSdo))
    {
      continue;
    }
#endif

    nCount++;
    sMappedTo += (sMappedTo.length() > 0 ? "," : "");
    sMappedTo += pSdo->gtw_getFullName();
  }

  for (i = m_SdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWSlaveDataObject* pSdo = m_SdoWriteBoundList[i];
#if USE_OPC_44
    if (dynamic_cast<GTWOpcSlaveDataObject*>(pSdo))
    {
      continue;
    }
#endif
#if USE_OPC_UA
    if (dynamic_cast<GTWOpcUaSlaveDataObject*>(pSdo))
    {
      continue;
    }
#endif

    nCount++;
    sMappedTo += (sMappedTo.length() > 0 ? "," : "");
    sMappedTo += pSdo->gtw_getFullName();
  }

  // check for MDO mappings
  for (i = m_MdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWMasterDataObject* pMdo = m_MdoWriteBoundList[i];
    sMappedTo += (sMappedTo.length() > 0 ? "," : "");
    sMappedTo += pMdo->getBdo() ? pMdo->getBdo()->GetFullName() : pMdo->getMdoUserTagName();
    nCount++;
  }

  std::list<GTWEquationDataObject*> list;
  GetDependentEquations(list);
  for (auto iter : list)
  {
    GTWEquationDataObject* pEq = iter;
    sMappedTo += (sMappedTo.length() > 0 ? "," : "");
    sMappedTo += pEq->GetFullName();
    nCount++;
  }

  return nCount > 0;
}

/**********************************************************************************\
	Function :			GTWMasterDataObject::getCnvtrType
	Description : [none]	
	Return :			GTWCNVTR_TYPE	-	
	Parameters :
			void	-	
	Note : [none]
\**********************************************************************************/
GTWCNVTR_TYPE GTWMasterDataObject::getCnvtrType(void)
{
  switch(getMdoType())
  {
  case GTWDEFS_TYPE_BOOL:
    return GTWCNVTR_TYPE_BOOL;
    break;
  case GTWDEFS_TYPE_CHAR:
    return GTWCNVTR_TYPE_CHAR;
    break;
  case GTWDEFS_TYPE_UCHAR:
    return GTWCNVTR_TYPE_USHORT;
    break;
  case GTWDEFS_TYPE_SHORT:
    return GTWCNVTR_TYPE_SHORT;
    break;
  case GTWDEFS_TYPE_USHORT:
    return GTWCNVTR_TYPE_USHORT;
    break;
  case GTWDEFS_TYPE_LONG:
    return GTWCNVTR_TYPE_LONG;
    break;
  case GTWDEFS_TYPE_ULONG:
    return GTWCNVTR_TYPE_ULONG;
    break;
  case GTWDEFS_TYPE_SFLOAT:
    return GTWCNVTR_TYPE_SFLOAT;
    break;
  case GTWDEFS_TYPE_DOUBLE:
    return GTWCNVTR_TYPE_DOUBLE;
    break;
  case GTWDEFS_TYPE_INT64:
    return GTWCNVTR_TYPE_INT64;
    break;
  case GTWDEFS_TYPE_UINT64:
    return GTWCNVTR_TYPE_UINT64;
    break;
  case GTWDEFS_TYPE_STRING:
    return GTWCNVTR_TYPE_STRING;
    break;
  case GTWDEFS_TYPE_TIME:
    return GTWCNVTR_TYPE_TMWDTIME;
    break;
  default:
    return GTWCNVTR_TYPE_UNKNOWN;
    break;
  }
  return GTWCNVTR_TYPE_UNKNOWN;
}

void MdoWriteTimerInfo::OnTimer (void *pCallBackData) 
{ 
  // This is the timer callback for WRITE_PERIOD option which is now deprecated (DM 3/29/2016) and does not work - see CR 10354

  // If this needs to be implemented, the loop below needs to consider the other lists
  //    TMWVectorTemplate<GTWSlaveDataObject_PTR, 1> m_SdoWriteBoundList;
  //    TMWVectorTemplate<GTWMasterDataObject *, 1>  m_MdoWriteBoundList; // list of MDO's we will write to when there is a change event recieved
  // This list below m_MdoReadBoundList may apply as well
  // Also several converters are not implemented for the updateMdo to even work below - one reason this is now deprecated as of 3/29/2016

  GTWMasterDataObject *pMdo = (GTWMasterDataObject *)pCallBackData;
  for (int i=0;i<pMdo->m_MdoReadBoundList.getSize();i++)
  {
    pMdo->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED,pMdo->m_MdoReadBoundList[i]);
  }
  return; 
}

void GTWMasterDataObject::RefreshMdo (void) 
{ 
  for (int i=0;i<m_MdoReadBoundList.getSize();i++)
  {
    updateMDO(GTWDEFS_UPDTRSN_REFRESH,m_MdoReadBoundList[i]);
  }
  return; 
}

bool GTWMasterDataObject::ValidateXrefMdos(CStdString &mdoName)
{
  if (!g_sMdoXrefList.empty())
  {
    //POSITION pos;
    // Iterate through the list in head-to-tail order.
    for( auto pos = g_sMdoXrefList.begin(); pos != g_sMdoXrefList.end(); ++pos )
    {
      GTWMasterDataObject *pMdo;
      //mdoName = g_sMdoXrefList.GetNext(pos);
      mdoName = *pos;
      if ( GetGTWApp()->findMdo(mdoName,&pMdo) != GTWDEFS_STAT_SUCCESS)
      {
        return false;
      }
    }
  }
  return true;
}

TMWTYPES_DOUBLE GTWMasterDataObject::getVarientValueAsDouble(GtwVariant &varient)
{
  return varient.GetFloatValue();
}

#ifdef _DEBUG
void GTWMasterDataObject::dumpMappings()
{
  CStdString mdoName = this->getBdo()->GetFullName();

  int i;
  for (i = m_SdoReadBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWSlaveDataObject* pSdo = m_SdoReadBoundList[i];
    if (dynamic_cast<GTWOpcSlaveDataObject*>(pSdo) || dynamic_cast<GTWOpcUaSlaveDataObject*>(pSdo))
    {
      continue;
    }

    TRACE("%s : SdoReadBoundList[%d]=%s\n", mdoName.c_str(), i, pSdo->gtw_getFullName().c_str());
  }

  for (i = m_SdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWSlaveDataObject* pSdo = m_SdoWriteBoundList[i];
    if (dynamic_cast<GTWOpcSlaveDataObject*>(pSdo) || dynamic_cast<GTWOpcUaSlaveDataObject*>(pSdo))
    {
      continue;
    }

    TRACE("%s : m_SdoWriteBoundList[%d]=%s\n", mdoName.c_str(), i, pSdo->gtw_getFullName().c_str());
  }

  for (i = m_MdoWriteBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWMasterDataObject* pMdo = m_MdoWriteBoundList[i];
    CStdString sn = pMdo->getBdo() ? pMdo->getBdo()->GetFullName() : pMdo->getMdoUserTagName();

    TRACE("%s : m_MdoWriteBoundList[%d]=%s\n", mdoName.c_str(), i, sn.c_str());
  }

  for (i = m_MdoReadBoundList.getSize() - 1; i >= 0; i--)
  {
    GTWMasterDataObject* pMdo = m_MdoReadBoundList[i];
    CStdString sn = pMdo->getBdo() ? pMdo->getBdo()->GetFullName() : pMdo->getMdoUserTagName();

    TRACE("%s : m_MdoReadBoundList[%d]=%s\n", mdoName.c_str(), i, sn.c_str());
  }

}
#endif
