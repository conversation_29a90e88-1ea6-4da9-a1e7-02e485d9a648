import { Component, <PERSON>Ini<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { HealthWSApi } from '../data/wsApi/HealthWSApi';
import { TagsWSApi } from '../data/wsApi/TagsWSApi';
import { NodesWSApi } from '../data/wsApi/NodesWSApi';
import { WebSocketManagerService, WebSocketConnection } from '../services/websocket-manager.service';
import { AlertService } from '../modules/alert/alert.service';

@Component({
  selector: 'app-websocket-health-monitor',
  template: `
    <div class="websocket-status">
      <h3>WebSocket Connection Status</h3>
      <div class="connection-indicator" [class.connected]="healthConnected" [class.disconnected]="!healthConnected">
        Health: {{ healthStatus }}
      </div>
      <div class="connection-indicator" [class.connected]="tagsConnected" [class.disconnected]="!tagsConnected">
        Tags: {{ tagsConnected ? 'Connected' : 'Disconnected' }}
      </div>
      <div class="connection-indicator" [class.connected]="nodesConnected" [class.disconnected]="!nodesConnected">
        Nodes: {{ nodesConnected ? 'Connected' : 'Disconnected' }}
      </div>
      <button (click)="forceReconnectAll()" [disabled]="reconnectionInProgress">
        {{ reconnectionInProgress ? 'Reconnecting...' : 'Force Reconnect All' }}
      </button>
    </div>
  `,
  styles: [`
    .websocket-status {
      padding: 10px;
      border: 1px solid #ccc;
      border-radius: 5px;
      margin: 10px;
    }
    .connection-indicator {
      padding: 5px;
      margin: 2px 0;
      border-radius: 3px;
    }
    .connected {
      background-color: #d4edda;
      color: #155724;
    }
    .disconnected {
      background-color: #f8d7da;
      color: #721c24;
    }
    button {
      margin-top: 10px;
      padding: 5px 10px;
    }
    button:disabled {
      opacity: 0.6;
    }
  `]
})
export class WebSocketHealthMonitorComponent implements OnInit, OnDestroy {
  private subscriptions: Subscription[] = [];
  private tagsWebSocket: WebSocket;
  private nodesWebSocket: WebSocket;
  
  // Connection status
  public healthConnected = false;
  public healthStatus = 'Disconnected';
  public tagsConnected = false;
  public nodesConnected = false;
  public reconnectionInProgress = false;

  constructor(
    private healthWSApi: HealthWSApi,
    private tagsWSApi: TagsWSApi,
    private nodesWSApi: NodesWSApi,
    private wsManager: WebSocketManagerService,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.setupHealthMonitoring();
    this.setupWebSocketManager();
    this.initializeConnections();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.wsManager.unregisterConnection('health');
    this.wsManager.unregisterConnection('tags');
    this.wsManager.unregisterConnection('nodes');
  }

  private setupHealthMonitoring(): void {
    // Monitor health WebSocket connection state
    const healthStateSub = this.healthWSApi.connectionState$.subscribe(state => {
      this.healthStatus = state;
      this.healthConnected = state === 'connected';
      
      if (state === 'heartbeat_lost') {
        this.alertService.debug('Health WebSocket: Heartbeat lost, triggering full reconnection');
      }
    });
    this.subscriptions.push(healthStateSub);

    // Set up the callback for when health detects connection loss
    this.healthWSApi.setConnectionLostCallback(() => {
      this.alertService.debug('Health WebSocket: Connection lost callback triggered');
      this.wsManager.reconnectAllConnections();
    });
  }

  private setupWebSocketManager(): void {
    // Register WebSocket connections with the manager
    const healthConnection: WebSocketConnection = {
      name: 'health',
      reconnectFunction: () => this.healthWSApi.forceReconnect(),
      isConnected: () => this.healthWSApi.getConnectionState() === 'connected'
    };

    const tagsConnection: WebSocketConnection = {
      name: 'tags',
      reconnectFunction: () => this.reconnectTags(),
      isConnected: () => this.tagsWebSocket && this.tagsWebSocket.readyState === WebSocket.OPEN
    };

    const nodesConnection: WebSocketConnection = {
      name: 'nodes',
      reconnectFunction: () => this.reconnectNodes(),
      isConnected: () => this.nodesWebSocket && this.nodesWebSocket.readyState === WebSocket.OPEN
    };

    this.wsManager.registerConnection(healthConnection);
    this.wsManager.registerConnection(tagsConnection);
    this.wsManager.registerConnection(nodesConnection);

    // Listen to reconnection events
    const reconnectionSub = this.wsManager.reconnectionEvents$.subscribe(event => {
      if (event === 'reconnection_started') {
        this.reconnectionInProgress = true;
      } else if (event === 'reconnection_completed') {
        this.reconnectionInProgress = false;
      }
      this.alertService.debug(`WebSocket Manager: ${event}`);
    });
    this.subscriptions.push(reconnectionSub);
  }

  private initializeConnections(): void {
    // Initialize health WebSocket
    const healthSub = this.healthWSApi.getHealthData().subscribe(
      data => {
        // Handle health data
      },
      error => {
        this.alertService.debug(`Health WebSocket error: ${error}`);
      }
    );
    this.subscriptions.push(healthSub);

    // Initialize other WebSockets
    this.connectTags();
    this.connectNodes();
  }

  private connectTags(): void {
    const tagsSub = this.tagsWSApi.openWebsocket().subscribe(
      websocket => {
        this.tagsWebSocket = websocket;
        this.tagsConnected = true;
        
        // Monitor tags data
        const tagsDataSub = this.tagsWSApi.getTagsData(websocket).subscribe(
          data => {
            // Handle tags data
          },
          error => {
            this.tagsConnected = false;
          }
        );
        this.subscriptions.push(tagsDataSub);
      },
      error => {
        this.tagsConnected = false;
        this.alertService.debug(`Tags WebSocket connection failed: ${error}`);
      }
    );
    this.subscriptions.push(tagsSub);
  }

  private connectNodes(): void {
    const nodesSub = this.nodesWSApi.openWebsocket().subscribe(
      websocket => {
        this.nodesWebSocket = websocket;
        this.nodesConnected = true;
        
        websocket.onclose = () => {
          this.nodesConnected = false;
        };
      },
      error => {
        this.nodesConnected = false;
        this.alertService.debug(`Nodes WebSocket connection failed: ${error}`);
      }
    );
    this.subscriptions.push(nodesSub);
  }

  private reconnectTags(): void {
    if (this.tagsWebSocket) {
      this.tagsWebSocket.close();
    }
    this.tagsConnected = false;
    setTimeout(() => this.connectTags(), 1000);
  }

  private reconnectNodes(): void {
    if (this.nodesWebSocket) {
      this.nodesWebSocket.close();
    }
    this.nodesConnected = false;
    setTimeout(() => this.connectNodes(), 1000);
  }

  public forceReconnectAll(): void {
    this.wsManager.reconnectAllConnections();
  }
}
