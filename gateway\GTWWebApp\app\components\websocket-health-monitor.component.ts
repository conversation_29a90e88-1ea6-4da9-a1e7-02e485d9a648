import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import {
  HealthWSApi,
  TagsWSApi,
  NodesWSApi,
  LogWSApi,
  BroadcastEventWSApi,
  WebSocketManagerService,
  WebSocketConnection
} from '../data/wsApi/wsApi';
import { AlertService } from '../modules/alert/alert.service';

@Component({
  selector: 'app-websocket-health-monitor',
  template: `
    <div class="websocket-status">
      <h3>WebSocket Connection Status</h3>
      <div class="connection-indicator" [class.connected]="healthConnected" [class.disconnected]="!healthConnected">
        Health: {{ healthStatus }}
      </div>
      <div class="connection-indicator" [class.connected]="broadcastConnected" [class.disconnected]="!broadcastConnected">
        Broadcast: {{ broadcastStatus }}
      </div>
      <div class="connection-indicator" [class.connected]="tagsConnected" [class.disconnected]="!tagsConnected">
        Tags: {{ tagsConnected ? 'Connected' : 'Disconnected' }}
      </div>
      <div class="connection-indicator" [class.connected]="nodesConnected" [class.disconnected]="!nodesConnected">
        Nodes: {{ nodesConnected ? 'Connected' : 'Disconnected' }}
      </div>
      <div class="connection-indicator" [class.connected]="logsConnected" [class.disconnected]="!logsConnected">
        Logs: {{ logsConnected ? 'Connected' : 'Disconnected' }}
      </div>
      <button (click)="forceReconnectAll()" [disabled]="reconnectionInProgress">
        {{ reconnectionInProgress ? 'Reconnecting...' : 'Force Reconnect All' }}
      </button>
    </div>
  `,
  styles: [`
    .websocket-status {
      padding: 10px;
      border: 1px solid #ccc;
      border-radius: 5px;
      margin: 10px;
    }
    .connection-indicator {
      padding: 5px;
      margin: 2px 0;
      border-radius: 3px;
    }
    .connected {
      background-color: #d4edda;
      color: #155724;
    }
    .disconnected {
      background-color: #f8d7da;
      color: #721c24;
    }
    button {
      margin-top: 10px;
      padding: 5px 10px;
    }
    button:disabled {
      opacity: 0.6;
    }
  `]
})
export class WebSocketHealthMonitorComponent implements OnInit, OnDestroy {
  private subscriptions: Subscription[] = [];
  private websockets: { [key: string]: WebSocket } = {};

  // Connection status
  public healthConnected = false;
  public healthStatus = 'Disconnected';
  public broadcastConnected = false;
  public broadcastStatus = 'Disconnected';
  public tagsConnected = false;
  public nodesConnected = false;
  public logsConnected = false;
  public reconnectionInProgress = false;

  constructor(
    private healthWSApi: HealthWSApi,
    private broadcastEventWSApi: BroadcastEventWSApi,
    private tagsWSApi: TagsWSApi,
    private nodesWSApi: NodesWSApi,
    private logWSApi: LogWSApi,
    private wsManager: WebSocketManagerService,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.setupHealthMonitoring();
    this.setupWebSocketManager();
    this.initializeConnections();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.wsManager.unregisterConnection('health');
    this.wsManager.unregisterConnection('broadcast');
    this.wsManager.unregisterConnection('tags');
    this.wsManager.unregisterConnection('nodes');
    this.wsManager.unregisterConnection('logs');

    // Close all connections
    Object.values(this.websockets).forEach(ws => {
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    });

    this.healthWSApi.close();
    this.broadcastEventWSApi.close();
    this.tagsWSApi.close();
    this.nodesWSApi.close();
    this.logWSApi.close();
  }

  private setupHealthMonitoring(): void {
    // Monitor health WebSocket connection state
    const healthStateSub = this.healthWSApi.connectionState$.subscribe(state => {
      this.healthStatus = state;
      this.healthConnected = state === 'connected';

      if (state === 'heartbeat_lost') {
        this.alertService.debug('Health WebSocket: Heartbeat lost, triggering full reconnection');
      }
    });
    this.subscriptions.push(healthStateSub);

    // Monitor broadcast WebSocket connection state
    const broadcastStateSub = this.broadcastEventWSApi.connectionState$.subscribe(state => {
      this.broadcastStatus = state;
      this.broadcastConnected = state === 'connected';

      if (state === 'heartbeat_lost') {
        this.alertService.debug('Broadcast WebSocket: Heartbeat lost, triggering full reconnection');
      }
    });
    this.subscriptions.push(broadcastStateSub);

    // Set up the callback for when health detects connection loss
    this.healthWSApi.setConnectionLostCallback(() => {
      this.alertService.debug('Health WebSocket: Connection lost callback triggered');
      this.wsManager.reconnectAllConnections();
    });
  }

  private setupWebSocketManager(): void {
    // Register WebSocket connections with the manager
    const healthConnection: WebSocketConnection = {
      name: 'health',
      reconnectFunction: () => this.healthWSApi.forceReconnect(),
      isConnected: () => this.healthWSApi.getConnectionState() === 'connected'
    };

    const broadcastConnection: WebSocketConnection = {
      name: 'broadcast',
      reconnectFunction: () => this.broadcastEventWSApi.forceReconnect(),
      isConnected: () => this.broadcastEventWSApi.getConnectionState() === 'connected'
    };

    const tagsConnection: WebSocketConnection = {
      name: 'tags',
      reconnectFunction: () => this.reconnectTags(),
      isConnected: () => this.websockets.tags && this.websockets.tags.readyState === WebSocket.OPEN
    };

    const nodesConnection: WebSocketConnection = {
      name: 'nodes',
      reconnectFunction: () => this.reconnectNodes(),
      isConnected: () => this.websockets.nodes && this.websockets.nodes.readyState === WebSocket.OPEN
    };

    const logsConnection: WebSocketConnection = {
      name: 'logs',
      reconnectFunction: () => this.reconnectLogs(),
      isConnected: () => this.websockets.logs && this.websockets.logs.readyState === WebSocket.OPEN
    };

    this.wsManager.registerConnection(healthConnection);
    this.wsManager.registerConnection(broadcastConnection);
    this.wsManager.registerConnection(tagsConnection);
    this.wsManager.registerConnection(nodesConnection);
    this.wsManager.registerConnection(logsConnection);

    // Listen to reconnection events
    const reconnectionSub = this.wsManager.reconnectionEvents$.subscribe(event => {
      if (event === 'reconnection_started') {
        this.reconnectionInProgress = true;
      } else if (event === 'reconnection_completed') {
        this.reconnectionInProgress = false;
      }
      this.alertService.debug(`WebSocket Manager: ${event}`);
    });
    this.subscriptions.push(reconnectionSub);
  }

  private initializeConnections(): void {
    // Set up master coordination
    this.healthWSApi.setConnectionLostCallback(() => {
      this.alertService.debug('Health connection lost, triggering full reconnection');
      this.wsManager.reconnectAllConnections();
    });

    this.broadcastEventWSApi.setConnectionLostCallback(() => {
      this.wsManager.reconnectAllConnections();
    });

    this.tagsWSApi.setConnectionLostCallback(() => {
      this.wsManager.reconnectAllConnections();
    });

    this.nodesWSApi.setConnectionLostCallback(() => {
      this.wsManager.reconnectAllConnections();
    });

    this.logWSApi.setConnectionLostCallback(() => {
      this.wsManager.reconnectAllConnections();
    });

    // Initialize health WebSocket
    const healthSub = this.healthWSApi.getHealthData().subscribe(
      data => {
        // Handle health data
      },
      error => {
        this.alertService.debug(`Health WebSocket error: ${error}`);
      }
    );
    this.subscriptions.push(healthSub);

    // Initialize broadcast events WebSocket
    const broadcastSub = this.broadcastEventWSApi.getBroadcastEventData().subscribe(
      data => {
        // Handle broadcast event data
      },
      error => {
        this.alertService.debug(`Broadcast WebSocket error: ${error}`);
      }
    );
    this.subscriptions.push(broadcastSub);

    // Initialize other WebSockets
    this.connectTags();
    this.connectNodes();
    this.connectLogs();
  }

  private connectTags(): void {
    const tagsSub = this.tagsWSApi.openWebsocket().subscribe(
      websocket => {
        this.websockets.tags = websocket;
        this.tagsConnected = true;

        // Monitor tags data
        const tagsDataSub = this.tagsWSApi.getTagsData(websocket).subscribe(
          data => {
            // Handle tags data
          },
          error => {
            this.tagsConnected = false;
          }
        );
        this.subscriptions.push(tagsDataSub);
      },
      error => {
        this.tagsConnected = false;
        this.alertService.debug(`Tags WebSocket connection failed: ${error}`);
      }
    );
    this.subscriptions.push(tagsSub);
  }

  private connectNodes(): void {
    const nodesSub = this.nodesWSApi.openWebsocket().subscribe(
      websocket => {
        this.websockets.nodes = websocket;
        this.nodesConnected = true;

        // Monitor nodes data
        const nodesDataSub = this.nodesWSApi.getNodesData(websocket).subscribe(
          data => {
            // Handle nodes data
          },
          error => {
            this.nodesConnected = false;
          }
        );
        this.subscriptions.push(nodesDataSub);
      },
      error => {
        this.nodesConnected = false;
        this.alertService.debug(`Nodes WebSocket connection failed: ${error}`);
      }
    );
    this.subscriptions.push(nodesSub);
  }

  private connectLogs(): void {
    const logsSub = this.logWSApi.openWebsocket().subscribe(
      websocket => {
        this.websockets.logs = websocket;
        this.logsConnected = true;

        // Monitor log data
        const logsDataSub = this.logWSApi.getLogData(websocket).subscribe(
          data => {
            // Handle log data
          },
          error => {
            this.logsConnected = false;
          }
        );
        this.subscriptions.push(logsDataSub);
      },
      error => {
        this.logsConnected = false;
        this.alertService.debug(`Log WebSocket connection failed: ${error}`);
      }
    );
    this.subscriptions.push(logsSub);
  }

  private reconnectTags(): void {
    if (this.websockets.tags) {
      this.websockets.tags.close();
    }
    this.tagsConnected = false;
    setTimeout(() => this.connectTags(), 1000);
  }

  private reconnectNodes(): void {
    if (this.websockets.nodes) {
      this.websockets.nodes.close();
    }
    this.nodesConnected = false;
    setTimeout(() => this.connectNodes(), 1000);
  }

  private reconnectLogs(): void {
    if (this.websockets.logs) {
      this.websockets.logs.close();
    }
    this.logsConnected = false;
    setTimeout(() => this.connectLogs(), 1000);
  }

  public forceReconnectAll(): void {
    this.wsManager.reconnectAllConnections();
  }
}
