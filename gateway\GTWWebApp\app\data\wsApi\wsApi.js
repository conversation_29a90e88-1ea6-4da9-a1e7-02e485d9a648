System.register(["./TagsWSApi", "./NodesWSApi", "./LogWSApi", "./HealthWSApi", "./BroadcastEventWSApi", "../../services/websocket-manager.service"], function (exports_1, context_1) {
    "use strict";
    var TagsWSApi_1, NodesWSApi_1, LogWSApi_1, HealthWSApi_1, BroadcastEventWSApi_1, websocket_manager_service_1, WSAPIS, WSSERVICES;
    var __moduleName = context_1 && context_1.id;
    var exportedNames_1 = {
        "WSAPIS": true,
        "WSSERVICES": true
    };
    function exportStar_1(m) {
        var exports = {};
        for (var n in m) {
            if (n !== "default" && !exportedNames_1.hasOwnProperty(n)) exports[n] = m[n];
        }
        exports_1(exports);
    }
    return {
        setters: [
            function (TagsWSApi_2_1) {
                exportStar_1(TagsWSApi_2_1);
                TagsWSApi_1 = TagsWSApi_2_1;
            },
            function (NodesWSApi_2_1) {
                exportStar_1(NodesWSApi_2_1);
                NodesWSApi_1 = NodesWSApi_2_1;
            },
            function (LogWSApi_2_1) {
                exportStar_1(LogWSApi_2_1);
                LogWSApi_1 = LogWSApi_2_1;
            },
            function (HealthWSApi_2_1) {
                exportStar_1(HealthWSApi_2_1);
                HealthWSApi_1 = HealthWSApi_2_1;
            },
            function (BroadcastEventWSApi_2_1) {
                exportStar_1(BroadcastEventWSApi_2_1);
                BroadcastEventWSApi_1 = BroadcastEventWSApi_2_1;
            },
            function (websocket_manager_service_2_1) {
                exportStar_1(websocket_manager_service_2_1);
                websocket_manager_service_1 = websocket_manager_service_2_1;
            }
        ],
        execute: function () {
            exports_1("WSAPIS", WSAPIS = [TagsWSApi_1.TagsWSApi, NodesWSApi_1.NodesWSApi, LogWSApi_1.LogWSApi, HealthWSApi_1.HealthWSApi, BroadcastEventWSApi_1.BroadcastEventWSApi]);
            exports_1("WSSERVICES", WSSERVICES = [websocket_manager_service_1.WebSocketManagerService]);
        }
    };
});
//# sourceMappingURL=wsApi.js.map