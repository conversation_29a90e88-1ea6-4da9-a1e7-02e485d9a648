System.register(["@angular/core", "rxjs"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, rxjs_1, WebSocketManagerService;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (rxjs_1_1) {
                rxjs_1 = rxjs_1_1;
            }
        ],
        execute: function () {
            WebSocketManagerService = (function () {
                function WebSocketManagerService() {
                    this.connections = new Map();
                    this.reconnectionInProgress = false;
                    this.reconnectionSubject = new rxjs_1.Subject();
                    this.reconnectionEvents$ = this.reconnectionSubject.asObservable();
                }
                WebSocketManagerService.prototype.registerConnection = function (connection) {
                    this.connections.set(connection.name, connection);
                    console.log("WebSocket Manager: Registered connection '" + connection.name + "'");
                };
                WebSocketManagerService.prototype.unregisterConnection = function (name) {
                    this.connections.delete(name);
                    console.log("WebSocket Manager: Unregistered connection '" + name + "'");
                };
                WebSocketManagerService.prototype.reconnectAllConnections = function () {
                    var _this = this;
                    if (this.reconnectionInProgress) {
                        console.log('WebSocket Manager: Reconnection already in progress, skipping');
                        return;
                    }
                    this.reconnectionInProgress = true;
                    console.log('WebSocket Manager: Starting reconnection of all WebSockets');
                    this.reconnectionSubject.next('reconnection_started');
                    var delay = 0;
                    this.connections.forEach(function (connection, name) {
                        setTimeout(function () {
                            try {
                                console.log("WebSocket Manager: Reconnecting '" + name + "'");
                                connection.reconnectFunction();
                                _this.reconnectionSubject.next("reconnected_" + name);
                            }
                            catch (error) {
                                console.error("WebSocket Manager: Failed to reconnect '" + name + "':", error);
                                _this.reconnectionSubject.next("reconnection_failed_" + name);
                            }
                        }, delay);
                        delay += 1000;
                    });
                    setTimeout(function () {
                        _this.reconnectionInProgress = false;
                        _this.reconnectionSubject.next('reconnection_completed');
                        console.log('WebSocket Manager: Reconnection process completed');
                    }, delay + 2000);
                };
                WebSocketManagerService.prototype.getConnectionStatus = function () {
                    var status = {};
                    this.connections.forEach(function (connection, name) {
                        try {
                            status[name] = connection.isConnected();
                        }
                        catch (error) {
                            status[name] = false;
                        }
                    });
                    return status;
                };
                WebSocketManagerService.prototype.hasDisconnectedConnections = function () {
                    var status = this.getConnectionStatus();
                    return Object.values(status).some(function (connected) { return !connected; });
                };
                WebSocketManagerService.prototype.reconnectConnection = function (name) {
                    var connection = this.connections.get(name);
                    if (connection) {
                        try {
                            console.log("WebSocket Manager: Force reconnecting '" + name + "'");
                            connection.reconnectFunction();
                            this.reconnectionSubject.next("force_reconnected_" + name);
                        }
                        catch (error) {
                            console.error("WebSocket Manager: Failed to force reconnect '" + name + "':", error);
                            this.reconnectionSubject.next("force_reconnection_failed_" + name);
                        }
                    }
                    else {
                        console.warn("WebSocket Manager: Connection '" + name + "' not found for reconnection");
                    }
                };
                WebSocketManagerService = __decorate([
                    core_1.Injectable({
                        providedIn: 'root'
                    }),
                    __metadata("design:paramtypes", [])
                ], WebSocketManagerService);
                return WebSocketManagerService;
            }());
            exports_1("WebSocketManagerService", WebSocketManagerService);
        }
    };
});
//# sourceMappingURL=websocket-manager.service.js.map