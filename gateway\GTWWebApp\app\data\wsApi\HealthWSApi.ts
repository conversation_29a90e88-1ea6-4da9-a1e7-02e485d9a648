import { Injectable } from '@angular/core';
import { Subject, BehaviorSubject } from 'rxjs';
import { Observable, Observer } from "rxjs";
import { Configuration } from '../configuration';
import { AlertService } from "../../modules/alert/alert.service";
import { TranslateService } from "@ngx-translate/core";
import { share, map } from "rxjs/operators";

@Injectable()
export class HealthWSApi {
  public basePath: string = "ws://localhost/rest";
  public configuration: Configuration = new Configuration();
  public websocket: WebSocket;
  private subject: Subject<any>;
  private isReconnectNeeded = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000;

  // Heartbeat detection properties
  private lastHeartbeatTime: number = 0;
  private heartbeatInterval: number = 15000; // 15 seconds expected interval (server sends every 5s, so 3x safety margin)
  private heartbeatTimeoutId: any;
  private missedHeartbeats: number = 0;
  private maxMissedHeartbeats: number = 3; // 45 seconds total timeout (3 * 15s)

  // Connection state management
  private connectionState = new BehaviorSubject<string>('disconnected');
  public connectionState$ = this.connectionState.asObservable();

  // Callback for triggering other WebSocket reconnections
  private onConnectionLostCallback: () => void;

  constructor(private alertService: AlertService, private translate: TranslateService) { }

  public getHealthData(): Subject<any> {
    if (!this.subject)
      this.subject = this.create();
    return this.subject;
  }

  public close(): void {
    this.stopHeartbeatMonitoring();
    if (this.websocket) {
      this.websocket.close();
      this.subject = null;
    }
    this.connectionState.next('disconnected');
  }

  // Set callback for when connection is lost and other WebSockets need to reconnect
  public setConnectionLostCallback(callback: () => void): void {
    this.onConnectionLostCallback = callback;
  }

  // Get current connection state
  public getConnectionState(): string {
    return this.connectionState.value;
  }

  // Force reconnection of this WebSocket
  public forceReconnect(): void {
    this.alertService.debug("Health WebSocket: Forcing reconnection");
    this.close();
    this.reconnectAttempts = 0;
    this.subject = this.create();
  }

  private create(): Subject<any> {
    const observable = Observable.create(
      (obs: Observer<any>) => {
        this.createObservable(obs);
      }).pipe(share());

    const observer = {
      next: (data: Object) => {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
          this.websocket.send(JSON.stringify(data));
        }
      }
    };
    return Subject.create(observer, observable);
  }

  private createObservable(obs: Observer<any>) {
    if (this.configuration.apiKeys) {
      let wsUrl = "";
      if (location.protocol != 'https:')
        wsUrl = "ws://" + window.location.host + "/getHealth?token=" + this.configuration.apiKeys["Authorization"];
      else
        wsUrl = "wss://" + window.location.host + "/getHealth?token=" + this.configuration.apiKeys["Authorization"];

      this.websocket = new WebSocket(wsUrl);

      this.websocket.onopen = (openEvent) => {
        // Reset reconnect attempts on successful connection
        this.reconnectAttempts = 0;
        this.missedHeartbeats = 0;
        this.connectionState.next('connected');
        this.startHeartbeatMonitoring();

        this.translate.get("TR_WEBSOCKET_OPEN", { websocketName: "getHealth" }).subscribe(res => {
          this.alertService.debug(res);
        });
      };

      // Set up event handlers for the new connection - That is the most important part to keep the subscriber connected
      this.websocket.onmessage = (event) => {
        this.onHeartbeatReceived();
        obs.next(event);
      };

      // Don't initiate reconnect here - let onclose handle it
      // WebSocket spec indicates that onclose will be called after onerror
      this.websocket.onerror = (error) => { };

      this.websocket.onclose = (evt) => {
        this.stopHeartbeatMonitoring();
        this.connectionState.next('disconnected');

        // Don't null the subject here to maintain the subscription
        if (evt.reason === "restart") {
          this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getHealth", reason: evt.type }).subscribe(res => {
            this.alertService.debug(res);
            obs.error("restart");
          });
        }
        else {
          this.reconnectWS(obs);
        }
      };
    }
  }

  private reconnectWS(obs: Observer<any>) {
    // Try to reconnect
    this.reconnectAttempts++;
    this.connectionState.next('reconnecting');

    if (this.reconnectAttempts <= this.maxReconnectAttempts) {
      // Set timeout to reconnect
      setTimeout(() => {
        this.translate.get("TR_WEBSOCKET_REOPENING", { websocketName: "getHealth", reconnectAttempt: this.reconnectAttempts }).subscribe(res => {
          this.alertService.debug(res);
        });
        this.createObservable(obs);
      }, this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1)); // Exponential back-off
    }
    else {
      // Max reconnect attempts reached
      // Only now error the observable
      this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getHealth", reason: "Reopen fail" }).subscribe(res => {
        obs.error(res);
      });
      this.close();
    }
  }

  // Heartbeat monitoring methods
  private startHeartbeatMonitoring(): void {
    this.lastHeartbeatTime = Date.now();
    this.missedHeartbeats = 0;

    // Check for missed heartbeats every 10 seconds
    this.heartbeatTimeoutId = setInterval(() => {
      this.checkHeartbeat();
    }, 10000);
  }

  private stopHeartbeatMonitoring(): void {
    if (this.heartbeatTimeoutId) {
      clearInterval(this.heartbeatTimeoutId);
      this.heartbeatTimeoutId = null;
    }
  }

  private onHeartbeatReceived(): void {
    this.lastHeartbeatTime = Date.now();
    this.missedHeartbeats = 0;
  }

  private checkHeartbeat(): void {
    const now = Date.now();
    const timeSinceLastHeartbeat = now - this.lastHeartbeatTime;

    if (timeSinceLastHeartbeat > this.heartbeatInterval) {
      this.missedHeartbeats++;
      this.alertService.debug(`Health WebSocket: Missed heartbeat ${this.missedHeartbeats}/${this.maxMissedHeartbeats} (${timeSinceLastHeartbeat}ms since last)`);

      if (this.missedHeartbeats >= this.maxMissedHeartbeats) {
        this.alertService.debug("Health WebSocket: Max missed heartbeats reached, triggering reconnection");
        this.connectionState.next('heartbeat_lost');

        // Trigger reconnection of all WebSockets
        if (this.onConnectionLostCallback) {
          this.onConnectionLostCallback();
        }

        // Force reconnection of health WebSocket
        this.forceReconnect();
      }
    }
  }
}