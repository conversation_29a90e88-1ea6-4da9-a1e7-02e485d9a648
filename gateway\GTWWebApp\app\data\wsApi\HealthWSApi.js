System.register(["@angular/core", "rxjs", "../configuration", "../../modules/alert/alert.service", "@ngx-translate/core", "rxjs/operators"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, rxjs_1, rxjs_2, configuration_1, alert_service_1, core_2, operators_1, HealthWSApi;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (rxjs_1_1) {
                rxjs_1 = rxjs_1_1;
                rxjs_2 = rxjs_1_1;
            },
            function (configuration_1_1) {
                configuration_1 = configuration_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            },
            function (core_2_1) {
                core_2 = core_2_1;
            },
            function (operators_1_1) {
                operators_1 = operators_1_1;
            }
        ],
        execute: function () {
            HealthWSApi = (function () {
                function HealthWSApi(alertService, translate) {
                    this.alertService = alertService;
                    this.translate = translate;
                    this.basePath = "ws://localhost/rest";
                    this.configuration = new configuration_1.Configuration();
                    this.isReconnectNeeded = false;
                    this.reconnectAttempts = 0;
                    this.maxReconnectAttempts = 5;
                    this.reconnectInterval = 3000;
                    this.lastHeartbeatTime = 0;
                    this.heartbeatInterval = 15000;
                    this.missedHeartbeats = 0;
                    this.maxMissedHeartbeats = 3;
                    this.connectionState = new rxjs_1.BehaviorSubject('disconnected');
                    this.connectionState$ = this.connectionState.asObservable();
                }
                HealthWSApi.prototype.getHealthData = function () {
                    if (!this.subject)
                        this.subject = this.create();
                    return this.subject;
                };
                HealthWSApi.prototype.close = function () {
                    this.stopHeartbeatMonitoring();
                    if (this.websocket) {
                        this.websocket.close();
                        this.subject = null;
                    }
                    this.connectionState.next('disconnected');
                };
                HealthWSApi.prototype.setConnectionLostCallback = function (callback) {
                    this.onConnectionLostCallback = callback;
                };
                HealthWSApi.prototype.getConnectionState = function () {
                    return this.connectionState.value;
                };
                HealthWSApi.prototype.forceReconnect = function () {
                    this.alertService.debug("Health WebSocket: Forcing reconnection");
                    this.close();
                    this.reconnectAttempts = 0;
                    this.subject = this.create();
                };
                HealthWSApi.prototype.create = function () {
                    var _this = this;
                    var observable = rxjs_2.Observable.create(function (obs) {
                        _this.createObservable(obs);
                    }).pipe(operators_1.share());
                    var observer = {
                        next: function (data) {
                            if (_this.websocket && _this.websocket.readyState === WebSocket.OPEN) {
                                _this.websocket.send(JSON.stringify(data));
                            }
                        }
                    };
                    return rxjs_1.Subject.create(observer, observable);
                };
                HealthWSApi.prototype.createObservable = function (obs) {
                    var _this = this;
                    if (this.configuration.apiKeys) {
                        var wsUrl = "";
                        if (location.protocol != 'https:')
                            wsUrl = "ws://" + window.location.host + "/getHealth?token=" + this.configuration.apiKeys["Authorization"];
                        else
                            wsUrl = "wss://" + window.location.host + "/getHealth?token=" + this.configuration.apiKeys["Authorization"];
                        this.websocket = new WebSocket(wsUrl);
                        this.websocket.onopen = function (openEvent) {
                            _this.reconnectAttempts = 0;
                            _this.missedHeartbeats = 0;
                            _this.connectionState.next('connected');
                            _this.startHeartbeatMonitoring();
                            _this.translate.get("TR_WEBSOCKET_OPEN", { websocketName: "getHealth" }).subscribe(function (res) {
                                _this.alertService.debug(res);
                            });
                        };
                        this.websocket.onmessage = function (event) {
                            _this.onHeartbeatReceived();
                            obs.next(event);
                        };
                        this.websocket.onerror = function (error) { };
                        this.websocket.onclose = function (evt) {
                            _this.stopHeartbeatMonitoring();
                            _this.connectionState.next('disconnected');
                            if (evt.reason === "restart") {
                                _this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getHealth", reason: evt.type }).subscribe(function (res) {
                                    _this.alertService.debug(res);
                                    obs.error("restart");
                                });
                            }
                            else {
                                _this.reconnectWS(obs);
                            }
                        };
                    }
                };
                HealthWSApi.prototype.reconnectWS = function (obs) {
                    var _this = this;
                    this.reconnectAttempts++;
                    this.connectionState.next('reconnecting');
                    if (this.reconnectAttempts <= this.maxReconnectAttempts) {
                        setTimeout(function () {
                            _this.translate.get("TR_WEBSOCKET_REOPENING", { websocketName: "getHealth", reconnectAttempt: _this.reconnectAttempts }).subscribe(function (res) {
                                _this.alertService.debug(res);
                            });
                            _this.createObservable(obs);
                        }, this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1));
                    }
                    else {
                        this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getHealth", reason: "Reopen fail" }).subscribe(function (res) {
                            obs.error(res);
                        });
                        this.close();
                    }
                };
                HealthWSApi.prototype.startHeartbeatMonitoring = function () {
                    var _this = this;
                    this.lastHeartbeatTime = Date.now();
                    this.missedHeartbeats = 0;
                    this.heartbeatTimeoutId = setInterval(function () {
                        _this.checkHeartbeat();
                    }, 10000);
                };
                HealthWSApi.prototype.stopHeartbeatMonitoring = function () {
                    if (this.heartbeatTimeoutId) {
                        clearInterval(this.heartbeatTimeoutId);
                        this.heartbeatTimeoutId = null;
                    }
                };
                HealthWSApi.prototype.onHeartbeatReceived = function () {
                    this.lastHeartbeatTime = Date.now();
                    this.missedHeartbeats = 0;
                };
                HealthWSApi.prototype.checkHeartbeat = function () {
                    var now = Date.now();
                    var timeSinceLastHeartbeat = now - this.lastHeartbeatTime;
                    if (timeSinceLastHeartbeat > this.heartbeatInterval) {
                        this.missedHeartbeats++;
                        this.alertService.debug("Health WebSocket: Missed heartbeat " + this.missedHeartbeats + "/" + this.maxMissedHeartbeats + " (" + timeSinceLastHeartbeat + "ms since last)");
                        if (this.missedHeartbeats >= this.maxMissedHeartbeats) {
                            this.alertService.debug("Health WebSocket: Max missed heartbeats reached, triggering reconnection");
                            this.connectionState.next('heartbeat_lost');
                            if (this.onConnectionLostCallback) {
                                this.onConnectionLostCallback();
                            }
                            this.forceReconnect();
                        }
                    }
                };
                HealthWSApi = __decorate([
                    core_1.Injectable(),
                    __metadata("design:paramtypes", [alert_service_1.AlertService, core_2.TranslateService])
                ], HealthWSApi);
                return HealthWSApi;
            }());
            exports_1("HealthWSApi", HealthWSApi);
        }
    };
});
//# sourceMappingURL=HealthWSApi.js.map