/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWS14CommandSdo.cpp                                                */
/* DESCRIPTION:  Definitions of SDO types for command 101 points             */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 14 ***_NOBODY_*** "                                                      */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#include "stdafx.h"
#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif


#include "GTWS14CommandSdo.h"


ImplementClassBaseInfo(GTWS14CommandSdo, GTWIecBaseDataObject, pClassInfo1, NULL)

/**********************************************************************************\
	Function :			GTWS14CommandSdo::GTWS14CommandSdo
	Description : [none]	
	Return :			constructor	-	
	Parameters :
			  TMWTYPES_UINT     infoObjAddr	-	
			  S870SCL_CTRL_MASK ctrlMask	-	
			  bool      synchronizeDownstream	-	
			  TMWTYPES_UINT     pulseLen	-	
	Note : [none]
\**********************************************************************************/
GTWS14CommandSdo::GTWS14CommandSdo(                  /* constructor */
  TMWTYPES_UINT     infoObjAddr,
  S870SCL_CTRL_MASK  ctrlMask,
  bool      synchronizeDownstream
  )
  :
  m_S14AllowedCtrlMask(ctrlMask),
  m_bSyncDownstream(synchronizeDownstream),
  m_cmdStatus(TMWDEFS_CMD_STAT_FAILED)
{
  setInfoObjAddr(infoObjAddr);
}

/**********************************************************************************\
	Function :			GTWS14CommandSdo::writeSdoCB
	Description : [none]	
	Return :			void	-	
	Parameters :
			GTWDEFS_CTRL_STAT newCtrlStat	-	
	Note : [none]
\**********************************************************************************/
  void GTWS14CommandSdo::writeSdoCB(GTWDEFS_CTRL_STAT newCtrlStat, void *pExtraCBData)
{
  switch(newCtrlStat)
  {
    case(GTWDEFS_CTRL_STAT_SUCCESS):
    {
      m_cmdStatus = TMWDEFS_CMD_STAT_SUCCESS;
      processWriteComplete(newCtrlStat);
      break;
    }
    case(GTWDEFS_CTRL_STAT_PENDING):
    {
      /* no change */
      break;
    }
    case(GTWDEFS_CTRL_STAT_CNFMD_PENDING):
    {
      if (m_cmdStatus == TMWDEFS_CMD_STAT_EXECUTING)
      {
        m_cmdStatus = TMWDEFS_CMD_STAT_MONITORING; 
      }
      break;
    }
    case(GTWDEFS_CTRL_STAT_FAILURE):
    default:
    {
      m_cmdStatus = TMWDEFS_CMD_STAT_FAILED; 
      processWriteComplete(newCtrlStat);
      break;
    }
  }
}

/**********************************************************************************\
	Function :			GTWS14CommandSdo::getCmdStatus
	Description : [none]	
	Return :			TMWDEFS_COMMAND_STATUS	-	
	Parameters :
			void *pPoint	-	
	Note : [none]
\**********************************************************************************/
TMWDEFS_COMMAND_STATUS GTWS14CommandSdo::getCmdStatus( void *pPoint)
{
  if (pPoint == TMWDEFS_NULL)
  {
    return(TMWDEFS_CMD_STAT_FAILED); 
  }

  GTWS14CommandSdo *pCmdSdo = (GTWS14CommandSdo *)pPoint;

  return(pCmdSdo->m_cmdStatus);
}

/**********************************************************************************\
	Function :			GTWS14CommandSdo::SelectRequired
	Description : [none]	
	Return :			bool	-	
	Parameters :
			void *pPoint	-	
	Note : [none]
\**********************************************************************************/
bool GTWS14CommandSdo::SelectRequired(void *pPoint)
{
  bool bSelectRequired = TMWDEFS_TRUE;
  if (pPoint)
  {
    GTWS14CommandSdo *pSdo = (GTWS14CommandSdo*)pPoint;
    if ((S870SCL_CTRL_MASK_NO_SELECT & pSdo->getS14AllowedCtrlMask()) == S870SCL_CTRL_MASK_NO_SELECT)
    {
      bSelectRequired = TMWDEFS_FALSE;
    }
  }
  return bSelectRequired;
}

bool GTWS14CommandSdo::GetValueColText(CStdString& itemStr)
{
  //char* pFormat = "value=%-20val quality=%04qly time=%s";
  itemStr = "value=" + m_sCurValue + "     quality=0000 time=";
  if (m_sCurValue == "unknown")
  {
    itemStr += "unknown";
  }
  else
  {
    CStdString sTime = GetUpdatedTimeString();
    itemStr += sTime;
    itemStr += "(A)"; // set to assumed time, i.e. the updated time
  }
  return true;
}

S870SCL_CTRL_MASK GTWS14CommandSdo::getS14RequestedCtrl(TMWTYPES_UCHAR sco)
{
  S870SCL_CTRL_MASK requestedCtrl = 0x00;
  if ((sco & I14DEF_QOC_QU_MASK) == I14DEF_QOC_QU_SHORT_PULSE)
  {
    requestedCtrl = S870SCL_CTRL_MASK_SHORT_PULSE;
  }
  if ((sco & I14DEF_QOC_QU_MASK) == I14DEF_QOC_QU_LONG_PULSE)
  {
    requestedCtrl = S870SCL_CTRL_MASK_LONG_PULSE;
  }
  if ((sco & I14DEF_QOC_QU_MASK) == I14DEF_QOC_QU_PERSISTENT)
  {
    requestedCtrl = S870SCL_CTRL_MASK_PERSISTENT;
  }
  if ((sco & I14DEF_QOC_QU_MASK) == I14DEF_QOC_QU_USE_DEFAULT)
  {
    requestedCtrl = S870SCL_CTRL_MASK_USE_DEFAULT;
  }

  return requestedCtrl;
}

bool GTWS14CommandSdo::IsAllowed(TMWTYPES_UCHAR sco)
{
  S870SCL_CTRL_MASK a = getS14AllowedCtrlMask();
  S870SCL_CTRL_MASK r = getS14RequestedCtrl(sco);

  // If NO_SELECT is allowed, permit any control operation (single-pass controls)
  if (a & S870SCL_CTRL_MASK_NO_SELECT)
  {
    return true;
  }

  // Otherwise, check for specific pulse type matches
  if ((a & r) != 0)
  {
    return true;
  }
  return false;
}

GTWDEFS_STAT GTWS14CommandSdo::ParseOptionsField(const char* connectionToken, const char** ppOptionString)
{
  if (ParseOptionsString(ppOptionString, s_GTWS14CommandSdo_SDO_allowedOptions[0].name))
  {
    m_bSyncDownstream = TMWDEFS_TRUE;
  }
  else if (ParseOptionsString(ppOptionString, s_GTWS14CommandSdo_SDO_allowedOptions[1].name))
  {
    m_bSyncDownstream = TMWDEFS_FALSE;
  }
  else
  {
    return (GTWSlaveDataObjectTemplate<GTWIecBaseDataObject>::ParseOptionsField(connectionToken, ppOptionString));
  }

  return GTWDEFS_STAT_SUCCESS;
}

void GTWS14CommandSdo::GetAllowedOptions(GTWDEFS_PARSED_OPTION_ARRAY& optionsArray)
{
  int i;
  for (i = 0; i < TMWDEFS_ARRAY_LENGTH(s_GTWS14CommandSdo_SDO_allowedOptions); i++)
  {
    optionsArray.push_back(s_GTWS14CommandSdo_SDO_allowedOptions[i]);
  }
  GTWSlaveDataObjectTemplate<GTWIecBaseDataObject>::GetAllowedOptions(optionsArray);
}