System.register(["@angular/core", "../data/wsApi/wsApi", "../modules/alert/alert.service"], function (exports_1, context_1) {
    "use strict";
    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __metadata = (this && this.__metadata) || function (k, v) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
    };
    var core_1, wsApi_1, alert_service_1, WebSocketHealthMonitorComponent;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (core_1_1) {
                core_1 = core_1_1;
            },
            function (wsApi_1_1) {
                wsApi_1 = wsApi_1_1;
            },
            function (alert_service_1_1) {
                alert_service_1 = alert_service_1_1;
            }
        ],
        execute: function () {
            WebSocketHealthMonitorComponent = (function () {
                function WebSocketHealthMonitorComponent(healthWSApi, broadcastEventWSApi, tagsWSApi, nodesWSApi, logWSApi, wsManager, alertService) {
                    this.healthWSApi = healthWSApi;
                    this.broadcastEventWSApi = broadcastEventWSApi;
                    this.tagsWSApi = tagsWSApi;
                    this.nodesWSApi = nodesWSApi;
                    this.logWSApi = logWSApi;
                    this.wsManager = wsManager;
                    this.alertService = alertService;
                    this.subscriptions = [];
                    this.websockets = {};
                    this.healthConnected = false;
                    this.healthStatus = 'Disconnected';
                    this.broadcastConnected = false;
                    this.broadcastStatus = 'Disconnected';
                    this.tagsConnected = false;
                    this.nodesConnected = false;
                    this.logsConnected = false;
                    this.reconnectionInProgress = false;
                }
                WebSocketHealthMonitorComponent.prototype.ngOnInit = function () {
                    this.setupHealthMonitoring();
                    this.setupWebSocketManager();
                    this.initializeConnections();
                };
                WebSocketHealthMonitorComponent.prototype.ngOnDestroy = function () {
                    this.subscriptions.forEach(function (sub) { return sub.unsubscribe(); });
                    this.wsManager.unregisterConnection('health');
                    this.wsManager.unregisterConnection('broadcast');
                    this.wsManager.unregisterConnection('tags');
                    this.wsManager.unregisterConnection('nodes');
                    this.wsManager.unregisterConnection('logs');
                    Object.values(this.websockets).forEach(function (ws) {
                        if (ws && ws.readyState === WebSocket.OPEN) {
                            ws.close();
                        }
                    });
                    this.healthWSApi.close();
                    this.broadcastEventWSApi.close();
                    this.tagsWSApi.close();
                    this.nodesWSApi.close();
                    this.logWSApi.close();
                };
                WebSocketHealthMonitorComponent.prototype.setupHealthMonitoring = function () {
                    var _this = this;
                    var healthStateSub = this.healthWSApi.connectionState$.subscribe(function (state) {
                        _this.healthStatus = state;
                        _this.healthConnected = state === 'connected';
                        if (state === 'heartbeat_lost') {
                            _this.alertService.debug('Health WebSocket: Heartbeat lost, triggering full reconnection');
                        }
                    });
                    this.subscriptions.push(healthStateSub);
                    var broadcastStateSub = this.broadcastEventWSApi.connectionState$.subscribe(function (state) {
                        _this.broadcastStatus = state;
                        _this.broadcastConnected = state === 'connected';
                        if (state === 'heartbeat_lost') {
                            _this.alertService.debug('Broadcast WebSocket: Heartbeat lost, triggering full reconnection');
                        }
                    });
                    this.subscriptions.push(broadcastStateSub);
                    this.healthWSApi.setConnectionLostCallback(function () {
                        _this.alertService.debug('Health WebSocket: Connection lost callback triggered');
                        _this.wsManager.reconnectAllConnections();
                    });
                };
                WebSocketHealthMonitorComponent.prototype.setupWebSocketManager = function () {
                    var _this = this;
                    var healthConnection = {
                        name: 'health',
                        reconnectFunction: function () { return _this.healthWSApi.forceReconnect(); },
                        isConnected: function () { return _this.healthWSApi.getConnectionState() === 'connected'; }
                    };
                    var broadcastConnection = {
                        name: 'broadcast',
                        reconnectFunction: function () { return _this.broadcastEventWSApi.forceReconnect(); },
                        isConnected: function () { return _this.broadcastEventWSApi.getConnectionState() === 'connected'; }
                    };
                    var tagsConnection = {
                        name: 'tags',
                        reconnectFunction: function () { return _this.reconnectTags(); },
                        isConnected: function () { return _this.websockets.tags && _this.websockets.tags.readyState === WebSocket.OPEN; }
                    };
                    var nodesConnection = {
                        name: 'nodes',
                        reconnectFunction: function () { return _this.reconnectNodes(); },
                        isConnected: function () { return _this.websockets.nodes && _this.websockets.nodes.readyState === WebSocket.OPEN; }
                    };
                    var logsConnection = {
                        name: 'logs',
                        reconnectFunction: function () { return _this.reconnectLogs(); },
                        isConnected: function () { return _this.websockets.logs && _this.websockets.logs.readyState === WebSocket.OPEN; }
                    };
                    this.wsManager.registerConnection(healthConnection);
                    this.wsManager.registerConnection(broadcastConnection);
                    this.wsManager.registerConnection(tagsConnection);
                    this.wsManager.registerConnection(nodesConnection);
                    this.wsManager.registerConnection(logsConnection);
                    var reconnectionSub = this.wsManager.reconnectionEvents$.subscribe(function (event) {
                        if (event === 'reconnection_started') {
                            _this.reconnectionInProgress = true;
                        }
                        else if (event === 'reconnection_completed') {
                            _this.reconnectionInProgress = false;
                        }
                        _this.alertService.debug("WebSocket Manager: " + event);
                    });
                    this.subscriptions.push(reconnectionSub);
                };
                WebSocketHealthMonitorComponent.prototype.initializeConnections = function () {
                    var _this = this;
                    this.healthWSApi.setConnectionLostCallback(function () {
                        _this.alertService.debug('Health connection lost, triggering full reconnection of ALL WebSockets');
                        _this.wsManager.reconnectAllConnections();
                    });
                    var healthSub = this.healthWSApi.getHealthData().subscribe(function (data) {
                    }, function (error) {
                        _this.alertService.debug("Health WebSocket error: " + error);
                    });
                    this.subscriptions.push(healthSub);
                    var broadcastSub = this.broadcastEventWSApi.getBroadcastEventData().subscribe(function (data) {
                    }, function (error) {
                        _this.alertService.debug("Broadcast WebSocket error: " + error);
                    });
                    this.subscriptions.push(broadcastSub);
                    this.connectTags();
                    this.connectNodes();
                    this.connectLogs();
                };
                WebSocketHealthMonitorComponent.prototype.connectTags = function () {
                    var _this = this;
                    var tagsSub = this.tagsWSApi.openWebsocket().subscribe(function (websocket) {
                        _this.websockets.tags = websocket;
                        _this.tagsConnected = true;
                        var tagsDataSub = _this.tagsWSApi.getTagsData(websocket).subscribe(function (data) {
                        }, function (error) {
                            _this.tagsConnected = false;
                        });
                        _this.subscriptions.push(tagsDataSub);
                    }, function (error) {
                        _this.tagsConnected = false;
                        _this.alertService.debug("Tags WebSocket connection failed: " + error);
                    });
                    this.subscriptions.push(tagsSub);
                };
                WebSocketHealthMonitorComponent.prototype.connectNodes = function () {
                    var _this = this;
                    var nodesSub = this.nodesWSApi.openWebsocket().subscribe(function (websocket) {
                        _this.websockets.nodes = websocket;
                        _this.nodesConnected = true;
                        var nodesDataSub = _this.nodesWSApi.getNodesData(websocket).subscribe(function (data) {
                        }, function (error) {
                            _this.nodesConnected = false;
                        });
                        _this.subscriptions.push(nodesDataSub);
                    }, function (error) {
                        _this.nodesConnected = false;
                        _this.alertService.debug("Nodes WebSocket connection failed: " + error);
                    });
                    this.subscriptions.push(nodesSub);
                };
                WebSocketHealthMonitorComponent.prototype.connectLogs = function () {
                    var _this = this;
                    var logsSub = this.logWSApi.openWebsocket().subscribe(function (websocket) {
                        _this.websockets.logs = websocket;
                        _this.logsConnected = true;
                        var logsDataSub = _this.logWSApi.getLogData(websocket).subscribe(function (data) {
                        }, function (error) {
                            _this.logsConnected = false;
                        });
                        _this.subscriptions.push(logsDataSub);
                    }, function (error) {
                        _this.logsConnected = false;
                        _this.alertService.debug("Log WebSocket connection failed: " + error);
                    });
                    this.subscriptions.push(logsSub);
                };
                WebSocketHealthMonitorComponent.prototype.reconnectTags = function () {
                    var _this = this;
                    if (this.websockets.tags) {
                        this.websockets.tags.close();
                    }
                    this.tagsConnected = false;
                    setTimeout(function () { return _this.connectTags(); }, 1000);
                };
                WebSocketHealthMonitorComponent.prototype.reconnectNodes = function () {
                    var _this = this;
                    if (this.websockets.nodes) {
                        this.websockets.nodes.close();
                    }
                    this.nodesConnected = false;
                    setTimeout(function () { return _this.connectNodes(); }, 1000);
                };
                WebSocketHealthMonitorComponent.prototype.reconnectLogs = function () {
                    var _this = this;
                    if (this.websockets.logs) {
                        this.websockets.logs.close();
                    }
                    this.logsConnected = false;
                    setTimeout(function () { return _this.connectLogs(); }, 1000);
                };
                WebSocketHealthMonitorComponent.prototype.forceReconnectAll = function () {
                    this.wsManager.reconnectAllConnections();
                };
                WebSocketHealthMonitorComponent = __decorate([
                    core_1.Component({
                        selector: 'app-websocket-health-monitor',
                        template: "\n    <div class=\"websocket-status\">\n      <h3>WebSocket Connection Status</h3>\n      <div class=\"connection-indicator\" [class.connected]=\"healthConnected\" [class.disconnected]=\"!healthConnected\">\n        Health: {{ healthStatus }}\n      </div>\n      <div class=\"connection-indicator\" [class.connected]=\"broadcastConnected\" [class.disconnected]=\"!broadcastConnected\">\n        Broadcast: {{ broadcastStatus }}\n      </div>\n      <div class=\"connection-indicator\" [class.connected]=\"tagsConnected\" [class.disconnected]=\"!tagsConnected\">\n        Tags: {{ tagsConnected ? 'Connected' : 'Disconnected' }}\n      </div>\n      <div class=\"connection-indicator\" [class.connected]=\"nodesConnected\" [class.disconnected]=\"!nodesConnected\">\n        Nodes: {{ nodesConnected ? 'Connected' : 'Disconnected' }}\n      </div>\n      <div class=\"connection-indicator\" [class.connected]=\"logsConnected\" [class.disconnected]=\"!logsConnected\">\n        Logs: {{ logsConnected ? 'Connected' : 'Disconnected' }}\n      </div>\n      <button (click)=\"forceReconnectAll()\" [disabled]=\"reconnectionInProgress\">\n        {{ reconnectionInProgress ? 'Reconnecting...' : 'Force Reconnect All' }}\n      </button>\n    </div>\n  ",
                        styles: ["\n    .websocket-status {\n      padding: 10px;\n      border: 1px solid #ccc;\n      border-radius: 5px;\n      margin: 10px;\n    }\n    .connection-indicator {\n      padding: 5px;\n      margin: 2px 0;\n      border-radius: 3px;\n    }\n    .connected {\n      background-color: #d4edda;\n      color: #155724;\n    }\n    .disconnected {\n      background-color: #f8d7da;\n      color: #721c24;\n    }\n    button {\n      margin-top: 10px;\n      padding: 5px 10px;\n    }\n    button:disabled {\n      opacity: 0.6;\n    }\n  "]
                    }),
                    __metadata("design:paramtypes", [wsApi_1.HealthWSApi,
                        wsApi_1.BroadcastEventWSApi,
                        wsApi_1.TagsWSApi,
                        wsApi_1.NodesWSApi,
                        wsApi_1.LogWSApi,
                        wsApi_1.WebSocketManagerService,
                        alert_service_1.AlertService])
                ], WebSocketHealthMonitorComponent);
                return WebSocketHealthMonitorComponent;
            }());
            exports_1("WebSocketHealthMonitorComponent", WebSocketHealthMonitorComponent);
        }
    };
});
//# sourceMappingURL=websocket-health-monitor.component.js.map