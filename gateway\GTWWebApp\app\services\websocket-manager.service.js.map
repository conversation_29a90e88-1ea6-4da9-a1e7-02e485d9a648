{"version": 3, "file": "websocket-manager.service.js", "sourceRoot": "", "sources": ["websocket-manager.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;gBAoBE;oBAPQ,gBAAW,GAAqC,IAAI,GAAG,EAAE,CAAC;oBAC1D,2BAAsB,GAAG,KAAK,CAAC;oBAC/B,wBAAmB,GAAG,IAAI,cAAO,EAAU,CAAC;oBAG7C,wBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,CAAC;gBAErD,CAAC;gBAGV,oDAAkB,GAAzB,UAA0B,UAA+B;oBACvD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;oBAClD,OAAO,CAAC,GAAG,CAAC,+CAA6C,UAAU,CAAC,IAAI,MAAG,CAAC,CAAC;gBAC/E,CAAC;gBAGM,sDAAoB,GAA3B,UAA4B,IAAY;oBACtC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBAC9B,OAAO,CAAC,GAAG,CAAC,iDAA+C,IAAI,MAAG,CAAC,CAAC;gBACtE,CAAC;gBAGM,yDAAuB,GAA9B;oBAAA,iBAgCC;oBA/BC,IAAI,IAAI,CAAC,sBAAsB,EAAE;wBAC/B,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAC;wBAC7E,OAAO;qBACR;oBAED,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;oBACnC,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;oBAC1E,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;oBAGtD,IAAI,KAAK,GAAG,CAAC,CAAC;oBACd,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,UAAU,EAAE,IAAI;wBACxC,UAAU,CAAC;4BACT,IAAI;gCACF,OAAO,CAAC,GAAG,CAAC,sCAAoC,IAAI,MAAG,CAAC,CAAC;gCACzD,UAAU,CAAC,iBAAiB,EAAE,CAAC;gCAC/B,KAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAe,IAAM,CAAC,CAAC;6BACtD;4BAAC,OAAO,KAAK,EAAE;gCACd,OAAO,CAAC,KAAK,CAAC,6CAA2C,IAAI,OAAI,EAAE,KAAK,CAAC,CAAC;gCAC1E,KAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,yBAAuB,IAAM,CAAC,CAAC;6BAC9D;wBACH,CAAC,EAAE,KAAK,CAAC,CAAC;wBACV,KAAK,IAAI,IAAI,CAAC;oBAChB,CAAC,CAAC,CAAC;oBAGH,UAAU,CAAC;wBACT,KAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;wBACpC,KAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;wBACxD,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;oBACnE,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC;gBACnB,CAAC;gBAGM,qDAAmB,GAA1B;oBACE,IAAM,MAAM,GAA+B,EAAE,CAAC;oBAC9C,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,UAAU,EAAE,IAAI;wBACxC,IAAI;4BACF,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;yBACzC;wBAAC,OAAO,KAAK,EAAE;4BACd,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;yBACtB;oBACH,CAAC,CAAC,CAAC;oBACH,OAAO,MAAM,CAAC;gBAChB,CAAC;gBAGM,4DAA0B,GAAjC;oBACE,IAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC1C,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAA,SAAS,IAAI,OAAA,CAAC,SAAS,EAAV,CAAU,CAAC,CAAC;gBAC7D,CAAC;gBAGM,qDAAmB,GAA1B,UAA2B,IAAY;oBACrC,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAC9C,IAAI,UAAU,EAAE;wBACd,IAAI;4BACF,OAAO,CAAC,GAAG,CAAC,4CAA0C,IAAI,MAAG,CAAC,CAAC;4BAC/D,UAAU,CAAC,iBAAiB,EAAE,CAAC;4BAC/B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,uBAAqB,IAAM,CAAC,CAAC;yBAC5D;wBAAC,OAAO,KAAK,EAAE;4BACd,OAAO,CAAC,KAAK,CAAC,mDAAiD,IAAI,OAAI,EAAE,KAAK,CAAC,CAAC;4BAChF,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,+BAA6B,IAAM,CAAC,CAAC;yBACpE;qBACF;yBAAM;wBACL,OAAO,CAAC,IAAI,CAAC,oCAAkC,IAAI,iCAA8B,CAAC,CAAC;qBACpF;gBACH,CAAC;gBA3FU,uBAAuB;oBAHnC,iBAAU,CAAC;wBACV,UAAU,EAAE,MAAM;qBACnB,CAAC;;mBACW,uBAAuB,CA4FnC;gBAAD,8BAAC;aAAA,AA5FD;;QA6FA,CAAC"}