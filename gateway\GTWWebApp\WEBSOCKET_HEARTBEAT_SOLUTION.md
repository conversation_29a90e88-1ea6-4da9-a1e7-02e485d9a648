# WebSocket Heartbeat and Connection Reset Solution

## Overview

This solution implements client-side WebSocket connection monitoring and automatic reconnection to address the communication issues between GTWEngine and GTWWebMonitor processes.

## Problem Addressed

- WebSocket connections fail after ~8 minutes of operation
- Server processes remain active but become unresponsive
- SSL/TLS connection failures cascade across multiple WebSockets
- Single-threaded server design causes blocking when under load

## Solution Components

### 1. Enhanced HealthWSApi (`HealthWSApi.ts`)

**New Features:**
- **Heartbeat Detection**: Monitors incoming messages every 10 seconds
- **Connection State Management**: Tracks connection status with observable
- **Automatic Reconnection**: Triggers reconnection after 3 missed heartbeats (90 seconds)
- **Cascade Trigger**: Notifies other WebSockets when connection is lost

**Key Properties:**
```typescript
private heartbeatInterval: number = 30000; // Expected message interval
private maxMissedHeartbeats: number = 3;   // Tolerance before reconnection
private connectionState = new BehaviorSubject<string>('disconnected');
```

### 2. WebSocket Manager Service (`websocket-manager.service.ts`)

**Purpose:**
- Centralized management of all WebSocket connections
- Coordinated reconnection with staggered delays
- Connection status monitoring
- Event broadcasting for UI updates

**Key Methods:**
- `registerConnection()`: Register WebSocket for management
- `reconnectAllConnections()`: Trigger coordinated reconnection
- `getConnectionStatus()`: Get status of all connections

### 3. Health Monitor Component (`websocket-health-monitor.component.ts`)

**Features:**
- Visual connection status indicators
- Manual reconnection button
- Real-time status updates
- Integration example for other components

## Implementation Steps

### Step 1: Update Your Main Component

```typescript
import { HealthWSApi } from './data/wsApi/HealthWSApi';
import { WebSocketManagerService } from './services/websocket-manager.service';

export class YourMainComponent implements OnInit {
  constructor(
    private healthWSApi: HealthWSApi,
    private wsManager: WebSocketManagerService
  ) {}

  ngOnInit() {
    // Set up health monitoring
    this.healthWSApi.setConnectionLostCallback(() => {
      console.log('Health connection lost, reconnecting all WebSockets');
      this.wsManager.reconnectAllConnections();
    });

    // Register your WebSocket connections
    this.wsManager.registerConnection({
      name: 'tags',
      reconnectFunction: () => this.reconnectTags(),
      isConnected: () => this.tagsWebSocket?.readyState === WebSocket.OPEN
    });
  }
}
```

### Step 2: Monitor Connection State

```typescript
// Subscribe to health connection state
this.healthWSApi.connectionState$.subscribe(state => {
  console.log('Health WebSocket state:', state);
  // Update UI accordingly
});

// Subscribe to reconnection events
this.wsManager.reconnectionEvents$.subscribe(event => {
  console.log('WebSocket Manager event:', event);
});
```

### Step 3: Handle Connection Loss

The system automatically:
1. Detects when health messages stop arriving
2. Waits for 3 missed heartbeats (90 seconds total)
3. Triggers reconnection of all registered WebSockets
4. Uses staggered delays to prevent server overload

## Configuration Options

### Heartbeat Timing
```typescript
// In HealthWSApi.ts
private heartbeatInterval: number = 30000;     // Expected interval between messages
private maxMissedHeartbeats: number = 3;       // Tolerance before reconnection
```

### Reconnection Settings
```typescript
// In HealthWSApi.ts
private maxReconnectAttempts = 5;              // Max reconnection attempts
private reconnectInterval = 3000;              // Base reconnection delay
```

## Benefits

1. **Proactive Detection**: Identifies connection issues before they affect users
2. **Automatic Recovery**: No manual intervention required
3. **Coordinated Reconnection**: Prevents server overload during recovery
4. **Visual Feedback**: Users can see connection status and manually trigger reconnection
5. **Minimal Server Changes**: Works with existing single-threaded server design

## Monitoring and Debugging

### Console Logs
The solution provides detailed logging:
- Heartbeat status updates
- Connection state changes
- Reconnection attempts
- Manager events

### Connection Status
Check connection status programmatically:
```typescript
const status = this.wsManager.getConnectionStatus();
console.log('All connections:', status);

const healthState = this.healthWSApi.getConnectionState();
console.log('Health connection:', healthState);
```

## Testing

1. **Normal Operation**: Verify heartbeat detection works during normal message flow
2. **Connection Loss**: Simulate server unresponsiveness and verify automatic reconnection
3. **Manual Reconnection**: Test the force reconnection functionality
4. **Multiple Failures**: Verify staggered reconnection prevents server overload

## Future Enhancements

1. **Exponential Backoff**: Implement smarter reconnection timing
2. **Connection Pooling**: Add connection reuse for better performance
3. **Health Metrics**: Track connection reliability statistics
4. **User Notifications**: Add toast notifications for connection events
