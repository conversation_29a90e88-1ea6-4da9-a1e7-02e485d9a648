import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

export interface WebSocketConnection {
  name: string;
  reconnectFunction: () => void;
  isConnected: () => boolean;
}

@Injectable({
  providedIn: 'root'
})
export class WebSocketManagerService {
  private connections: Map<string, WebSocketConnection> = new Map();
  private reconnectionInProgress = false;
  private reconnectionSubject = new Subject<string>();
  
  // Observable for components to listen to reconnection events
  public reconnectionEvents$ = this.reconnectionSubject.asObservable();

  constructor() { }

  // Register a WebSocket connection for management
  public registerConnection(connection: WebSocketConnection): void {
    this.connections.set(connection.name, connection);
    console.log(`WebSocket Manager: Registered connection '${connection.name}'`);
  }

  // Unregister a WebSocket connection
  public unregisterConnection(name: string): void {
    this.connections.delete(name);
    console.log(`WebSocket Manager: Unregistered connection '${name}'`);
  }

  // Trigger reconnection of all registered WebSockets
  public reconnectAllConnections(): void {
    if (this.reconnectionInProgress) {
      console.log('WebSocket Manager: Reconnection already in progress, skipping');
      return;
    }

    this.reconnectionInProgress = true;
    console.log('WebSocket Manager: Starting reconnection of all WebSockets');
    this.reconnectionSubject.next('reconnection_started');

    // Reconnect all connections with a small delay between each
    let delay = 0;
    this.connections.forEach((connection, name) => {
      setTimeout(() => {
        try {
          console.log(`WebSocket Manager: Reconnecting '${name}'`);
          connection.reconnectFunction();
          this.reconnectionSubject.next(`reconnected_${name}`);
        } catch (error) {
          console.error(`WebSocket Manager: Failed to reconnect '${name}':`, error);
          this.reconnectionSubject.next(`reconnection_failed_${name}`);
        }
      }, delay);
      delay += 1000; // 1 second delay between reconnections
    });

    // Reset the reconnection flag after all attempts
    setTimeout(() => {
      this.reconnectionInProgress = false;
      this.reconnectionSubject.next('reconnection_completed');
      console.log('WebSocket Manager: Reconnection process completed');
    }, delay + 2000);
  }

  // Get status of all connections
  public getConnectionStatus(): { [key: string]: boolean } {
    const status: { [key: string]: boolean } = {};
    this.connections.forEach((connection, name) => {
      try {
        status[name] = connection.isConnected();
      } catch (error) {
        status[name] = false;
      }
    });
    return status;
  }

  // Check if any connections are disconnected
  public hasDisconnectedConnections(): boolean {
    const status = this.getConnectionStatus();
    return Object.values(status).some(connected => !connected);
  }

  // Force reconnection of a specific connection
  public reconnectConnection(name: string): void {
    const connection = this.connections.get(name);
    if (connection) {
      try {
        console.log(`WebSocket Manager: Force reconnecting '${name}'`);
        connection.reconnectFunction();
        this.reconnectionSubject.next(`force_reconnected_${name}`);
      } catch (error) {
        console.error(`WebSocket Manager: Failed to force reconnect '${name}':`, error);
        this.reconnectionSubject.next(`force_reconnection_failed_${name}`);
      }
    } else {
      console.warn(`WebSocket Manager: Connection '${name}' not found for reconnection`);
    }
  }
}
