{"version": 3, "file": "TagsWSApi.js", "sourceRoot": "", "sources": ["TagsWSApi.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAgCE,mBAAoB,YAA0B,EAAU,SAA2B;oBAA/D,iBAAY,GAAZ,YAAY,CAAc;oBAAU,cAAS,GAAT,SAAS,CAAkB;oBAvB5E,aAAQ,GAAW,qBAAqB,CAAC;oBAC1C,kBAAa,GAAkB,IAAI,6BAAa,EAAE,CAAC;oBAIjD,sBAAiB,GAAG,CAAC,CAAC;oBACtB,yBAAoB,GAAG,CAAC,CAAC;oBACzB,sBAAiB,GAAG,IAAI,CAAC;oBAGzB,sBAAiB,GAAW,CAAC,CAAC;oBAC9B,sBAAiB,GAAW,KAAK,CAAC;oBAElC,qBAAgB,GAAW,CAAC,CAAC;oBAC7B,wBAAmB,GAAW,CAAC,CAAC;oBAGhC,oBAAe,GAAG,IAAI,sBAAe,CAAS,cAAc,CAAC,CAAC;oBAC/D,qBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;gBAM9D,CAAC;gBAGM,6CAAyB,GAAhC,UAAiC,QAAoB;oBACnD,IAAI,CAAC,wBAAwB,GAAG,QAAQ,CAAC;gBAC3C,CAAC;gBAGM,sCAAkB,GAAzB;oBACE,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;gBACpC,CAAC;gBAGM,kCAAc,GAArB;oBACE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;oBAChE,IAAI,CAAC,KAAK,EAAE,CAAC;oBACb,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;gBAE7B,CAAC;gBAGM,yBAAK,GAAZ;oBACE,IAAI,CAAC,uBAAuB,EAAE,CAAC;oBAC/B,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACzB,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;wBAC9B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;qBAC9B;oBACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC5C,CAAC;gBAEM,iCAAa,GAApB;oBAAA,iBA+BC;oBA7BC,IAAI,KAAK,GAAG,EAAE,CAAC;oBACf,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ;wBAC/B,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;;wBAEzG,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;oBAE5G,IAAI,SAAS,GAAG,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC;oBACrC,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;oBAElC,OAAO,iBAAU,CAAC,MAAM,CAAC,UAAA,QAAQ;wBAC/B,SAAS,CAAC,MAAM,GAAG,UAAC,SAAS;4BAC3B,KAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;4BAC3B,KAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;4BAC1B,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;4BACvC,KAAI,CAAC,wBAAwB,EAAE,CAAC;4BAEhC,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;gCACjF,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;4BAC/B,CAAC,CAAC,CAAC;4BACH,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;4BACzB,QAAQ,CAAC,QAAQ,EAAE,CAAC;wBACtB,CAAC,CAAC;wBAEF,SAAS,CAAC,OAAO,GAAG,UAAC,KAAK;4BACxB,KAAI,CAAC,uBAAuB,EAAE,CAAC;4BAC/B,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;4BACnC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;wBACxB,CAAC,CAAC;oBACJ,CAAC,CAAC,CAAA;gBACJ,CAAC;gBAEK,+BAAW,GAAlB,UAAmB,SAAoB;oBAAvC,iBA4BC;oBA3BA,OAAO,iBAAU,CAAC,MAAM,CAAC,UAAA,QAAQ;wBAChC,SAAS,CAAC,SAAS,GAAG,UAAC,GAAG;4BACrB,KAAI,CAAC,mBAAmB,EAAE,CAAC;4BAC/B,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBACpB,CAAC,CAAC;wBAEC,SAAS,CAAC,OAAO,GAAG,UAAC,GAAG;4BACtB,KAAI,CAAC,uBAAuB,EAAE,CAAC;4BAC/B,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;4BAE1C,IAAI,GAAG,CAAC,MAAM,KAAK,YAAY,EAAE;gCAC/B,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;oCACxG,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gCAC/B,CAAC,CAAC,CAAC;6BACJ;iCACI;gCACH,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;oCACpG,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gCAC/B,CAAC,CAAC,CAAC;gCAGH,KAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;6BACpC;4BACD,QAAQ,CAAC,QAAQ,EAAE,CAAC;wBACtB,CAAC,CAAC;oBACN,CAAC,CAAC;yBACC,IAAI,CAAC,iBAAK,EAAE,CAAC,CAAC;gBAClB,CAAC;gBAGQ,4CAAwB,GAAhC;oBAAA,iBAQC;oBAPC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBACpC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;oBAG1B,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC;wBACpC,KAAI,CAAC,cAAc,EAAE,CAAC;oBACxB,CAAC,EAAE,KAAK,CAAC,CAAC;gBACZ,CAAC;gBAEO,2CAAuB,GAA/B;oBACE,IAAI,IAAI,CAAC,kBAAkB,EAAE;wBAC3B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBACvC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;qBAChC;gBACH,CAAC;gBAEO,uCAAmB,GAA3B;oBACE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBACpC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;gBAC5B,CAAC;gBAEO,kCAAc,GAAtB;oBACE,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBACvB,IAAM,sBAAsB,GAAG,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC;oBAE5D,IAAI,sBAAsB,GAAG,IAAI,CAAC,iBAAiB,EAAE;wBACnD,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBACxB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,sCAAoC,IAAI,CAAC,gBAAgB,SAAI,IAAI,CAAC,mBAAmB,UAAK,sBAAsB,mBAAgB,CAAC,CAAC;wBAE1J,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,mBAAmB,EAAE;4BACrD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,uEAAuE,CAAC,CAAC;4BACjG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;4BAI5C,IAAI,CAAC,cAAc,EAAE,CAAC;yBACvB;qBACF;gBACH,CAAC;gBAEO,uCAAmB,GAA3B,UAA4B,QAAa;oBAAzC,iBAiBC;oBAhBC,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;oBAE1C,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,oBAAoB,EAAE;wBACvD,UAAU,CAAC;4BACT,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,wBAAwB,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,gBAAgB,EAAE,KAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;gCAChI,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;4BAC/B,CAAC,CAAC,CAAC;wBAGL,CAAC,EAAE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;qBACxE;yBAAM;wBACL,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;4BACzG,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;wBAC/B,CAAC,CAAC,CAAC;qBACJ;gBACH,CAAC;gBAjLU,SAAS;oBADrB,iBAAU,EAAE;qDAyBuB,4BAAY,EAAqB,uBAAgB;mBAxBxE,SAAS,CAkLrB;gBAAD,gBAAC;aAAA,AAlLD;;QAkLC,CAAC"}