export * from './TagsWSApi';
import { Tags<PERSON>Api } from './TagsWSApi';
export * from './NodesWSApi';
import { NodesWSApi } from './NodesWSApi';
export * from './LogWSApi';
import { LogWSApi } from './LogWSApi';
export * from './HealthWSApi';
import { HealthWSApi } from './HealthWSApi';
export * from './BroadcastEventWSApi';
import { BroadcastEventWSApi } from './BroadcastEventWSApi';

// Export WebSocket Manager Service
export * from '../../services/websocket-manager.service';
import { WebSocketManagerService } from '../../services/websocket-manager.service';

export const WSAPIS = [TagsWSApi, NodesWSApi, LogWSApi, HealthWSApi, BroadcastEventWSApi];
export const WSSERVICES = [WebSocketManagerService];


