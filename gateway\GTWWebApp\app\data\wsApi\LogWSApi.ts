﻿import { Injectable } from "@angular/core";
import { Observable, BehaviorSubject, Subject } from "rxjs";
import { Configuration } from '../configuration';
import { AlertService } from "../../modules/alert/alert.service";
import { TranslateService } from "@ngx-translate/core";
import { share, map } from "rxjs/operators";

@Injectable()
export class LogWSApi {
  public basePath: string = "ws://localhost/rest";
	public configuration: Configuration = new Configuration();

  // WebSocket management
  private currentWebSocket: WebSocket;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000;

  // Heartbeat detection properties
  private lastHeartbeatTime: number = 0;
  private heartbeatInterval: number = 30000; // 30 seconds expected interval
  private heartbeatTimeoutId: any;
  private missedHeartbeats: number = 0;
  private maxMissedHeartbeats: number = 3;

  // Connection state management
  private connectionState = new BehaviorSubject<string>('disconnected');
  public connectionState$ = this.connectionState.asObservable();

  // Callback for triggering other WebSocket reconnections
  private onConnectionLostCallback: () => void;

  constructor(private alertService: AlertService, private translate: TranslateService) {
	}


  // Set callback for when connection is lost and other WebSockets need to reconnect
  public setConnectionLostCallback(callback: () => void): void {
    this.onConnectionLostCallback = callback;
  }

  // Get current connection state
  public getConnectionState(): string {
    return this.connectionState.value;
  }

  // Force reconnection of this WebSocket
  public forceReconnect(): void {
    this.alertService.debug("Log WebSocket: Forcing reconnection");
    this.close();
    this.reconnectAttempts = 0;
    // Trigger new connection through openWebsocket
  }

  // Close current connection
  public close(): void {
    this.stopHeartbeatMonitoring();
    if (this.currentWebSocket) {
      this.currentWebSocket.close();
      this.currentWebSocket = null;
    }
    this.connectionState.next('disconnected');
  }

  public openWebsocket(): Observable<WebSocket> {
    // Use dynamic URL construction like other APIs
    let wsUrl = "";
    if (location.protocol != 'https:')
      wsUrl = "ws://" + window.location.host + "/getLogEntries?token=" + this.configuration.apiKeys["Authorization"];
    else
      wsUrl = "wss://" + window.location.host + "/getLogEntries?token=" + this.configuration.apiKeys["Authorization"];

    let websocket = new WebSocket(wsUrl);
    this.currentWebSocket = websocket;

    return Observable.create(observer => {
      websocket.onopen = (openEvent) => {
        this.reconnectAttempts = 0;
        this.missedHeartbeats = 0;
        this.connectionState.next('connected');
        this.startHeartbeatMonitoring();

        this.translate.get("TR_WEBSOCKET_OPEN", { websocketName: "getLogEntries" }).subscribe(res => {
          this.alertService.debug(res);
        });
        observer.next(websocket);
        observer.complete();
      };

      websocket.onerror = (error) => {
        this.stopHeartbeatMonitoring();
        this.connectionState.next('error');
        observer.error(error);
      };
    })
  }

  public getLogData(websocket: WebSocket): Observable<any> {
		return Observable.create(observer => {
			websocket.onmessage = (evt) => {
        this.onHeartbeatReceived();
				observer.next(evt);
      };

      websocket.onclose = (evt) => {
        this.stopHeartbeatMonitoring();
        this.connectionState.next('disconnected');

        if (evt.reason === "panelClose") {
          this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getLogEntries", reason: "panelClose" }).subscribe(res => {
            this.alertService.debug(res);
          });
        }
        else {
          this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getLogEntries", reason: evt.type }).subscribe(res => {
            this.alertService.debug(res);
          });

          // Attempt reconnection if not a manual close
          this.attemptReconnection(observer);
        }
        observer.complete();
      };
    })
    .pipe(share());
	}

  // Heartbeat monitoring methods
  private startHeartbeatMonitoring(): void {
    this.lastHeartbeatTime = Date.now();
    this.missedHeartbeats = 0;

    // Check for missed heartbeats every 10 seconds
    this.heartbeatTimeoutId = setInterval(() => {
      this.checkHeartbeat();
    }, 10000);
  }

  private stopHeartbeatMonitoring(): void {
    if (this.heartbeatTimeoutId) {
      clearInterval(this.heartbeatTimeoutId);
      this.heartbeatTimeoutId = null;
    }
  }

  private onHeartbeatReceived(): void {
    this.lastHeartbeatTime = Date.now();
    this.missedHeartbeats = 0;
  }

  private checkHeartbeat(): void {
    const now = Date.now();
    const timeSinceLastHeartbeat = now - this.lastHeartbeatTime;

    if (timeSinceLastHeartbeat > this.heartbeatInterval) {
      this.missedHeartbeats++;
      this.alertService.debug(`Log WebSocket: Missed heartbeat ${this.missedHeartbeats}/${this.maxMissedHeartbeats} (${timeSinceLastHeartbeat}ms since last)`);

      if (this.missedHeartbeats >= this.maxMissedHeartbeats) {
        this.alertService.debug("Log WebSocket: Max missed heartbeats reached, triggering reconnection");
        this.connectionState.next('heartbeat_lost');

        // Trigger reconnection of all WebSockets
        if (this.onConnectionLostCallback) {
          this.onConnectionLostCallback();
        }

        // Force reconnection of this WebSocket
        this.forceReconnect();
      }
    }
  }

  private attemptReconnection(observer: any): void {
    this.reconnectAttempts++;
    this.connectionState.next('reconnecting');

    if (this.reconnectAttempts <= this.maxReconnectAttempts) {
      setTimeout(() => {
        this.translate.get("TR_WEBSOCKET_REOPENING", { websocketName: "getLogEntries", reconnectAttempt: this.reconnectAttempts }).subscribe(res => {
          this.alertService.debug(res);
        });
        // Note: Reconnection would need to be handled by the calling component
        // since this API pattern separates connection creation from data handling
      }, this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1));
    } else {
      this.translate.get("TR_WEBSOCKET_CLOSE", { websocketName: "getLogEntries", reason: "Reopen fail" }).subscribe(res => {
        this.alertService.debug(res);
      });
    }
  }
}