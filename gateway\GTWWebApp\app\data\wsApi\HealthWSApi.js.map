{"version": 3, "file": "HealthWSApi.js", "sourceRoot": "", "sources": ["HealthWSApi.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAiCE,qBAAoB,YAA0B,EAAU,SAA2B;oBAA/D,iBAAY,GAAZ,YAAY,CAAc;oBAAU,cAAS,GAAT,SAAS,CAAkB;oBAvB5E,aAAQ,GAAW,qBAAqB,CAAC;oBACzC,kBAAa,GAAkB,IAAI,6BAAa,EAAE,CAAC;oBAGlD,sBAAiB,GAAG,KAAK,CAAC;oBAC1B,sBAAiB,GAAG,CAAC,CAAC;oBACtB,yBAAoB,GAAG,CAAC,CAAC;oBACzB,sBAAiB,GAAG,IAAI,CAAC;oBAGzB,sBAAiB,GAAW,CAAC,CAAC;oBAC9B,sBAAiB,GAAW,KAAK,CAAC;oBAElC,qBAAgB,GAAW,CAAC,CAAC;oBAC7B,wBAAmB,GAAW,CAAC,CAAC;oBAGhC,oBAAe,GAAG,IAAI,sBAAe,CAAS,cAAc,CAAC,CAAC;oBAC/D,qBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;gBAKyB,CAAC;gBAEjF,mCAAa,GAApB;oBACE,IAAI,CAAC,IAAI,CAAC,OAAO;wBACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;oBAC/B,OAAO,IAAI,CAAC,OAAO,CAAC;gBACtB,CAAC;gBAEM,2BAAK,GAAZ;oBACE,IAAI,CAAC,uBAAuB,EAAE,CAAC;oBAC/B,IAAI,IAAI,CAAC,SAAS,EAAE;wBAClB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;wBACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;qBACrB;oBACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC5C,CAAC;gBAGM,+CAAyB,GAAhC,UAAiC,QAAoB;oBACnD,IAAI,CAAC,wBAAwB,GAAG,QAAQ,CAAC;gBAC3C,CAAC;gBAGM,wCAAkB,GAAzB;oBACE,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;gBACpC,CAAC;gBAGM,oCAAc,GAArB;oBACE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC;oBAClE,IAAI,CAAC,KAAK,EAAE,CAAC;oBACb,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;oBAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC/B,CAAC;gBAEO,4BAAM,GAAd;oBAAA,iBAcC;oBAbC,IAAM,UAAU,GAAG,iBAAU,CAAC,MAAM,CAClC,UAAC,GAAkB;wBACjB,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAK,EAAE,CAAC,CAAC;oBAEnB,IAAM,QAAQ,GAAG;wBACf,IAAI,EAAE,UAAC,IAAY;4BACjB,IAAI,KAAI,CAAC,SAAS,IAAI,KAAI,CAAC,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE;gCAClE,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;6BAC3C;wBACH,CAAC;qBACF,CAAC;oBACF,OAAO,cAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBAC9C,CAAC;gBAEO,sCAAgB,GAAxB,UAAyB,GAAkB;oBAA3C,iBAgDC;oBA/CC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;wBAC9B,IAAI,KAAK,GAAG,EAAE,CAAC;wBACf,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ;4BAC/B,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;;4BAE3G,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;wBAE9G,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC;wBAEtC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,UAAC,SAAS;4BAEhC,KAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;4BAC3B,KAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;4BAC1B,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;4BACvC,KAAI,CAAC,wBAAwB,EAAE,CAAC;4BAEhC,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;gCACnF,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;4BAC/B,CAAC,CAAC,CAAC;wBACL,CAAC,CAAC;wBAGF,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,UAAC,KAAK;4BAC/B,KAAI,CAAC,mBAAmB,EAAE,CAAC;4BAC3B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAClB,CAAC,CAAC;wBAIF,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,UAAC,KAAK,IAAO,CAAC,CAAC;wBAExC,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,UAAC,GAAG;4BAC3B,KAAI,CAAC,uBAAuB,EAAE,CAAC;4BAC/B,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;4BAG1C,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE;gCAC5B,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;oCACtG,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oCAC7B,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gCACvB,CAAC,CAAC,CAAC;6BACJ;iCACI;gCACH,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;6BACvB;wBACH,CAAC,CAAC;qBACH;gBACH,CAAC;gBAEO,iCAAW,GAAnB,UAAoB,GAAkB;oBAAtC,iBAsBC;oBApBC,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;oBAE1C,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,oBAAoB,EAAE;wBAEvD,UAAU,CAAC;4BACT,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,wBAAwB,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,gBAAgB,EAAE,KAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;gCAClI,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;4BAC/B,CAAC,CAAC,CAAC;4BACH,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;wBAC7B,CAAC,EAAE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;qBACxE;yBACI;wBAGH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC,SAAS,CAAC,UAAA,GAAG;4BAC3G,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;wBACjB,CAAC,CAAC,CAAC;wBACH,IAAI,CAAC,KAAK,EAAE,CAAC;qBACd;gBACH,CAAC;gBAGO,8CAAwB,GAAhC;oBAAA,iBAQC;oBAPC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBACpC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;oBAG1B,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC;wBACpC,KAAI,CAAC,cAAc,EAAE,CAAC;oBACxB,CAAC,EAAE,KAAK,CAAC,CAAC;gBACZ,CAAC;gBAEO,6CAAuB,GAA/B;oBACE,IAAI,IAAI,CAAC,kBAAkB,EAAE;wBAC3B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBACvC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;qBAChC;gBACH,CAAC;gBAEO,yCAAmB,GAA3B;oBACE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBACpC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;gBAC5B,CAAC;gBAEO,oCAAc,GAAtB;oBACE,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBACvB,IAAM,sBAAsB,GAAG,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC;oBAE5D,IAAI,sBAAsB,GAAG,IAAI,CAAC,iBAAiB,EAAE;wBACnD,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBACxB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,wCAAsC,IAAI,CAAC,gBAAgB,SAAI,IAAI,CAAC,mBAAmB,UAAK,sBAAsB,mBAAgB,CAAC,CAAC;wBAE5J,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,mBAAmB,EAAE;4BACrD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,0EAA0E,CAAC,CAAC;4BACpG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;4BAG5C,IAAI,IAAI,CAAC,wBAAwB,EAAE;gCACjC,IAAI,CAAC,wBAAwB,EAAE,CAAC;6BACjC;4BAGD,IAAI,CAAC,cAAc,EAAE,CAAC;yBACvB;qBACF;gBACH,CAAC;gBAjMU,WAAW;oBADvB,iBAAU,EAAE;qDAyBuB,4BAAY,EAAqB,uBAAgB;mBAxBxE,WAAW,CAkMvB;gBAAD,kBAAC;aAAA,AAlMD;;QAkMC,CAAC"}